/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'

const AuthController = () => import('#controllers/auth_controller')
const UsersController = () => import('#controllers/users_controller')
const ChildrenController = () => import('#controllers/children_controller')
const StoriesController = () => import('#controllers/stories_controller')
const FilesController = () => import('#controllers/files_controller')
const CreditsController = () => import('#controllers/credits_controller')
const BannersController = () => import('#controllers/banners_controller')
const ChaptersController = () => import('#controllers/chapters_controller')
const VouchersController = () => import('#controllers/vouchers_controller')
const PreschoolsController = () => import('#controllers/preschools_controller')
const PacksController = () => import('#controllers/packs_controller')
const UserChatsController = () => import('#controllers/user_chats_controller')
const AdminChatsController = () => import('#controllers/admin_chats_controller')
const PushNotificationsController = () => import('#controllers/push_notifications_controller')
const RecordingsController = () => import('#controllers/recordings_controller')
const RecordingResultsController = () => import('#controllers/recording_results_controller')
const SessionsController = () => import('#controllers/sessions_controller')
const UserEventsController = () => import('#controllers/user_events_controller')
const PaymentsController = () => import('#controllers/payments_controller')
const WalletsController = () => import('#controllers/wallets_controller')
const PurchaseClickTrackersController = () => import('#controllers/purchase_click_trackers_controller')
const StoryOrdersController = () => import('#controllers/story_orders_controller')
const DevicesController = () => import('#controllers/devices_controller')
const SettingsController = () => import('#controllers/settings_controller')
const UserSettingsController = () => import('#controllers/user_settings_controller')
const VersionsController = () => import('#controllers/versions_controller')
const RedirectsController = () => import('#controllers/redirects_controller')
const ReportsController = () => import('#controllers/reports_controller')
const ReviewsController = () => import('#controllers/reviews_controller')
const StoryRatingsController = () => import('#controllers/story_ratings_controller')
const WebhooksController = () => import('#controllers/webhooks_controller')

// Newly migrated controllers
const BundlesController = () => import('#controllers/bundles_controller')
const PackCodesController = () => import('#controllers/pack_codes_controller')
const PackLevelsController = () => import('#controllers/pack_levels_controller')
const PlansController = () => import('#controllers/plans_controller')
const TagsController = () => import('#controllers/tags_controller')

router.get('/', async () => {
  return {
    hello: 'world',
  }
})

router.get('/success', async () => {
  return { success: true }
})

router.get('/cancel', async () => {
  return { success: false, message: 'Customer cancelled' }
})

// Public API routes
router.group(() => {
  // Auth routes
  router.post('/auth/register', [AuthController, 'register'])
  router.post('/auth/login', [AuthController, 'login'])
  router.post('/auth/login-magic', [AuthController, 'loginMagic'])
  router.post('/auth/forgot-password', [AuthController, 'forgotPassword'])
  router.post('/auth/send-auth-code', [AuthController, 'sendAuthCode'])
  router.post('/auth/verify-auth-code', [AuthController, 'verifyAuthCode'])

  // Public content routes
  router.get('/banners', [BannersController, 'find'])
  router.get('/packs', [PacksController, 'findAll'])
  router.get('/packs/:id', [PacksController, 'findOne'])
  router.get('/preschools', [PreschoolsController, 'findAll'])
  router.get('/stories', [StoriesController, 'findAll'])
  router.get('/stories/:id', [StoriesController, 'findOne'])
  router.get('/bundles', [BundlesController, 'findAll'])
  router.get('/plans', [PlansController, 'findPlans'])
  router.get('/plans/:id', [PlansController, 'findOnePlan'])
  router.get('/plans/handle/:handle', [PlansController, 'findPlanByHandle'])
  router.get('/plans/:id/stories', [PlansController, 'findPlanStories'])
  router.get('/tags', [TagsController, 'index'])

  // Voucher verification
  router.post('/vouchers/verify', [VouchersController, 'verify'])

  // Instant recordings (no auth required)
  router.post('/instant/recordings', [RecordingsController, 'instantStore'])
  router.post('/instant/recordings/poll', [RecordingsController, 'instantFind'])

  // Webhooks
  router.post('/webhook/wix', [WebhooksController, 'wixCallback'])
  router.post('/webhooks/stripe', [WebhooksController, 'stripeCallback'])

  // Redirect
  router.get('/redirect', [RedirectsController, 'redirect'])

  // System settings
  router.get('/system-settings/handle/:handle', [SettingsController, 'findByHandle'])
}).prefix('/api/v1')

// Authenticated user routes
router.group(() => {
  // User management
  router.get('/profile', [AuthController, 'profile'])
  router.put('/profile', [AuthController, 'updateProfile'])
  router.post('/change-password', [AuthController, 'changePassword'])
  router.post('/logout', [AuthController, 'logout'])

  // Children management
  router.get('/children', [ChildrenController, 'find'])
  router.post('/children', [ChildrenController, 'create'])
  router.put('/children/:id', [ChildrenController, 'update'])
  router.delete('/children/:id', [ChildrenController, 'delete'])

  // File uploads
  router.post('/files/upload', [FilesController, 'uploadFile'])
  router.post('/files/upload-my-file', [FilesController, 'uploadMyFile'])

  // User packs
  router.get('/my-packs', [PacksController, 'findMyPacks'])
  router.get('/user-packs', [PacksController, 'findUserPacks'])
  router.get('/user-packs/:id', [PacksController, 'findUserPack'])

  // User chats
  router.get('/chats', [UserChatsController, 'find'])
  router.get('/chats/:id', [UserChatsController, 'findOne'])
  router.post('/chats', [UserChatsController, 'create'])
  router.post('/chats/:id/histories', [UserChatsController, 'createHistory'])
  router.get('/chats/:id/histories', [UserChatsController, 'findHistories'])

  // Recordings
  router.post('/recordings', [RecordingsController, 'store'])
  router.post('/recordings/poll', [RecordingsController, 'find'])

  // Sessions
  router.post('/sessions', [SessionsController, 'create'])
  router.post('/sessions/:id/end', [SessionsController, 'endSession'])

  // Wallet
  router.get('/wallet', [WalletsController, 'findMyWallet'])
  router.get('/wallet-transactions', [WalletsController, 'findMyTransactions'])

  // Purchase tracking
  router.post('/purchase-click', [PurchaseClickTrackersController, 'clickTracker'])

  // Payments
  router.post('/app-purchase/waived', [PaymentsController, 'waivedPurchase'])
  router.post('/app-purchase/android', [PaymentsController, 'validatePurchaseAndroid'])
  router.post('/app-purchase/ios', [PaymentsController, 'validatePurchaseIOS'])

  // Devices
  router.post('/devices', [DevicesController, 'createMyToken'])

  // User settings
  router.get('/user-settings', [UserSettingsController, 'findUserSetting'])
  router.put('/user-settings/:id', [UserSettingsController, 'updateUserSetting'])

  // Story ratings
  router.get('/stories/:story_id/story-ratings', [StoryRatingsController, 'findMyStoryRating'])
  router.put('/story-ratings/:id', [StoryRatingsController, 'updateMyStoryRating'])

  // User subscriptions and plans
  router.get('/subscriptions', [PlansController, 'findMySubscriptions'])
  router.post('/checkout', [PlansController, 'createStripeCheckoutSession'])
}).prefix('/api/v1').middleware('auth')

// Admin routes
router.group(() => {
  // Auth
  router.post('/auth/login', [AuthController, 'adminLogin'])

  // User management
  router.get('/users', [UsersController, 'adminFind'])
  router.get('/users/:id', [UsersController, 'adminFindOne'])

  // Content management
  router.get('/banners', [BannersController, 'adminFind'])
  router.get('/banners/:id', [BannersController, 'findOne'])
  router.post('/banners', [BannersController, 'create'])
  router.put('/banners/:id', [BannersController, 'update'])
  router.delete('/banners/:id', [BannersController, 'delete'])

  router.get('/credits', [CreditsController, 'findAll'])
  router.put('/credits/:id', [CreditsController, 'update'])

  router.get('/chapters', [ChaptersController, 'findAll'])
  router.get('/chapters/:id', [ChaptersController, 'findOne'])
  router.post('/chapters', [ChaptersController, 'create'])
  router.put('/chapters/:id', [ChaptersController, 'update'])
  router.delete('/chapters/:id', [ChaptersController, 'deleteChapter'])

  router.get('/vouchers', [VouchersController, 'findAll'])
  router.post('/vouchers', [VouchersController, 'create'])

  router.get('/preschools', [PreschoolsController, 'adminFind'])
  router.get('/preschools/:id', [PreschoolsController, 'adminFindOne'])
  router.post('/preschools', [PreschoolsController, 'create'])
  router.put('/preschools/:id', [PreschoolsController, 'update'])

  router.get('/packs', [PacksController, 'adminFind'])
  router.get('/packs/:id', [PacksController, 'adminFindOne'])

  router.get('/stories', [StoriesController, 'adminFind'])
  router.get('/stories/:id', [StoriesController, 'adminFindOne'])

  // Admin chats
  router.get('/chats', [AdminChatsController, 'find'])
  router.get('/chats/:id', [AdminChatsController, 'findOne'])
  router.post('/chats', [AdminChatsController, 'create'])
  router.post('/chats/:id/histories', [AdminChatsController, 'createHistory'])
  router.get('/chats/:id/histories', [AdminChatsController, 'findHistories'])

  // Push notifications
  router.get('/notifications', [PushNotificationsController, 'find'])
  router.post('/notifications/create', [PushNotificationsController, 'sendAppNotification'])

  // Recording results
  router.get('/recordings', [RecordingResultsController, 'find'])
  router.get('/recordings/:id', [RecordingResultsController, 'findOne'])

  // User events
  router.get('/user-events', [UserEventsController, 'findAll'])

  // Transactions
  router.get('/transactions', [WalletsController, 'findTransactions'])
  router.post('/free-credits', [WalletsController, 'freeCredits'])

  // Story orders
  router.get('/story-orders', [StoryOrdersController, 'findAll'])
  router.get('/stories/:id/redemption', [StoryOrdersController, 'findUserStoryRedemption'])
  router.get('/preschools/:id/redemption', [StoryOrdersController, 'findUserPreschoolRedemption'])
  router.post('/users/preschool-access', [StoryOrdersController, 'blockUserPreschoolAccess'])

  // Settings
  router.get('/settings', [SettingsController, 'findAll'])
  router.post('/settings', [SettingsController, 'create'])
  router.put('/settings/:id', [SettingsController, 'update'])
  router.delete('/settings/:id', [SettingsController, 'deleteSetting'])

  // Versions
  router.get('/versions', [VersionsController, 'find'])
  router.get('/versions/:id', [VersionsController, 'findOne'])
  router.post('/versions/create', [VersionsController, 'create'])
  router.put('/versions/update/:id', [VersionsController, 'update'])
  router.delete('/versions/:id', [VersionsController, 'delete'])

  // Reports
  router.post('/reports', [ReportsController, 'create'])

  // Reviews
  router.get('/reviews', [ReviewsController, 'find'])
  router.get('/reviews/:id', [ReviewsController, 'findOne'])
  router.post('/reviews', [ReviewsController, 'create'])
  router.put('/reviews/:id', [ReviewsController, 'update'])
  router.delete('/reviews/:id', [ReviewsController, 'delete'])

  // Story ratings
  router.get('/story-ratings', [StoryRatingsController, 'findStoryRatings'])

  // Bundles (admin)
  router.get('/bundles', [BundlesController, 'adminFind'])
  router.get('/bundles/:id', [BundlesController, 'adminFindOne'])
  router.post('/bundles', [BundlesController, 'create'])
  router.put('/bundles/:id', [BundlesController, 'update'])
  router.post('/bundles/:id/add-stories', [BundlesController, 'addStoriesToBundle'])
  router.delete('/bundles/:id/remove-story/:story_id', [BundlesController, 'removeStoryFromBundle'])
  router.get('/bundles/:bundle_id/redemption', [BundlesController, 'findBundleRedemption'])

  // Pack codes (admin)
  router.get('/pack-codes', [PackCodesController, 'find'])
  router.get('/pack-codes/active', [PackCodesController, 'findActivePackCodes'])
  router.post('/pack-codes', [PackCodesController, 'create'])

  // Pack levels (admin)
  router.put('/pack-levels/:id/audio', [PackLevelsController, 'updatePackLevelAudio'])
  router.put('/pack-levels/:id/rive', [PackLevelsController, 'updatePackLevelRive'])

  // Plans (admin)
  router.get('/plans', [PlansController, 'adminFind'])
  router.get('/plans/:id', [PlansController, 'adminFindOne'])
  router.post('/plans', [PlansController, 'create'])
  router.put('/plans/:id', [PlansController, 'update'])
  router.get('/stripe/products', [PlansController, 'findStripeProducts'])

  // Tags (admin)
  router.get('/tags', [TagsController, 'adminIndex'])
  router.post('/tags', [TagsController, 'store'])
  router.put('/tags/:id', [TagsController, 'update'])
  router.delete('/tags/:id', [TagsController, 'destroy'])
}).prefix('/api/v1/admin').middleware('auth')
