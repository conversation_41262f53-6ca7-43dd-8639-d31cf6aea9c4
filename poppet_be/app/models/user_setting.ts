import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'

type UserSettingMetadata = {
  repeat_animation: number
}

export default class UserSetting extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare receiveUpdate: boolean

  @column()
  declare userId: number

  @column({ prepare: (values) => JSON.stringify(values) })
  declare metadata: UserSettingMetadata

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>
}
