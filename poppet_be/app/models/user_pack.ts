import { DateTime } from 'luxon'
import {
  BaseModel,
  beforeFetch,
  beforeFind,
  belongsTo,
  column,
  computed,
} from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import { isEmpty, flatten } from 'radash'

import User from './user.js'
import Pack from './pack.js'
import UserPackFilter from './filters/user_pack_filter.js'

export type StoryStatus = {
  story_id: number
  child_id: number
  completed: boolean
}

export type LevelStatus = {
  child_id: number
  level: number
  completed: boolean
  triggered: boolean
}

export type RiveInput = {
  key: string
  type: string
  story_id: number
  value?: boolean
}

export default class UserPack extends compose(BaseModel, Filterable) {
  static $filter = () => UserPackFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column()
  declare packId: number

  @belongsTo(() => Pack)
  declare pack: BelongsTo<typeof Pack>

  @column()
  declare currentLevel: number

  @column({ consume: (value: boolean) => Boolean(value) })
  declare blocked: boolean

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  declare storyStatuses: Array<StoryStatus>

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  declare levelStatuses: Array<LevelStatus>

  @computed({ serializeAs: 'current_rive' })
  get currentRive() {
    if (!isEmpty(this.pack?.packLevels)) {
      return this.pack.packLevels.find((packLevel) => packLevel.level === this.currentLevel)?.file
    }

    return null
  }

  @computed({ serializeAs: 'current_input' })
  get currentInput() {
    let inputs: RiveInput[] = []
    if (!isEmpty(this.pack?.packLevels)) {
      inputs =
        this.pack.packLevels.find((packLevel) => packLevel.level === this.currentLevel)?.inputs ?? []
    }

    if (inputs.length > 0) {
      if ((this.storyStatuses?.length ?? 0) > 0) {
        const finalInput: RiveInput[] = []
        inputs.forEach((input) => {
          const completedIndex = this.storyStatuses.findIndex(
            (status) =>
              input.story_id === status.story_id && status.child_id === this.user.activeChildId
          )

          if (completedIndex !== -1) {
            finalInput.push({ ...input, value: true })
          } else {
            finalInput.push({ ...input, value: false })
          }
        })

        return finalInput
      }

      return inputs
    }

    return null
  }

  @computed({ serializeAs: 'all_input' })
  get allInputs() {
    let inputs: RiveInput[] = []
    if (!isEmpty(this.pack?.packLevels)) {
      inputs = flatten(this.pack.packLevels.map((packLevel) => packLevel.inputs))
    }

    if (inputs.length > 0) {
      if ((this.storyStatuses?.length ?? 0) > 0) {
        const finalInput: RiveInput[] = []
        inputs.forEach((input) => {
          const completedIndex = this.storyStatuses.findIndex(
            (status) =>
              input.story_id === status.story_id && status.child_id === this.user.activeChildId
          )

          if (completedIndex !== -1) {
            finalInput.push({ ...input, value: true })
          } else {
            finalInput.push({ ...input, value: false })
          }
        })

        return finalInput
      }

      return inputs
    }

    return null
  }

  @column()
  declare wordCount: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // For Multiple rows.
  @beforeFetch()
  static notBlockedMultiple(query: ModelQueryBuilderContract<typeof UserPack>) {
    query.where('blocked', false)
  }

  // For single rows
  @beforeFind()
  static notBlockedSingle(query: ModelQueryBuilderContract<typeof UserPack>) {
    query.where('blocked', false)
  }
}
