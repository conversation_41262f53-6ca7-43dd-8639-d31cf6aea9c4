import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany, computed, beforeFind, beforeFetch } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import db from '@adonisjs/lucid/services/db'
import User from './user.js'
import ChatHistory from './chat_history.js'

export default class Chat extends compose(BaseModel, Filterable) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare firstName?: string

  @column()
  declare lastName?: string

  @column()
  declare userId: number

  @belongsTo(() => User, { foreignKey: 'userId' })
  declare user: BelongsTo<typeof User>

  @hasMany(() => ChatHistory, {
    foreignKey: 'chatId',
  })
  declare histories: Has<PERSON>any<typeof ChatHistory>

  @computed()
  get latest_message() {
    if (this.$preloaded.histories && this.histories.length > 0) {
      return this.histories[0].serialize()
    }
    return null
  }

  @computed()
  get latest_message_date() {
    return this.$extras.latest_message_date ?? null
  }

  @computed()
  get total_unread() {
    return this.$extras.total_unread ?? null
  }

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @beforeFind()
  static getLatestHistorySingle(query: ModelQueryBuilderContract<typeof Chat>) {
    query
      .select(
        db.from('chat_histories')
          .whereColumn('chat_histories.chat_id', 'chats.id')
          .max('chat_histories.created_at')
          .as('latest_message_date')
      )
      .select(
        db.from('chat_histories')
          .whereColumn('chat_histories.chat_id', 'chats.id')
          .whereNull('seen')
          .andWhere('is_deleted', false)
          .count('*')
          .as('total_unread')
      )
      .select('chats.*')
      .preload('histories', (query) => {
        query.where('is_deleted', false).orderBy('created_at', 'desc')
      })
  }

  @beforeFetch()
  static getLatestHistoryMultiple(query: ModelQueryBuilderContract<typeof Chat>) {
    query
      .select(
        db.from('chat_histories')
          .whereColumn('chat_histories.chat_id', 'chats.id')
          .max('chat_histories.created_at')
          .as('latest_message_date')
      )
      .select(
        db.from('chat_histories')
          .whereColumn('chat_histories.chat_id', 'chats.id')
          .whereNull('seen')
          .andWhere('is_deleted', false)
          .count('*')
          .as('total_unread')
      )
      .select('chats.*')
      .preload('histories', (query) => {
        query.where('is_deleted', false).orderBy('created_at', 'desc').limit(1)
      })
  }
}
