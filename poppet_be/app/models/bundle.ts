import { DateTime } from 'luxon'
import { BaseModel, column, computed, manyToMany } from '@adonisjs/lucid/orm'
import type { ManyToMany } from '@adonisjs/lucid/types/relations'
import Story from './story.js'

export default class Bundle extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare description: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare region: string | Array<string>

  @column()
  declare language: string

  @column({ consume: (value) => Number(value) })
  declare discount: number

  @column({ consume: (value: boolean) => Boolean(value) })
  declare blocked: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @manyToMany(() => Story, {
    pivotTable: 'bundle_stories',
    pivotForeignKey: 'bundle_id',
    pivotRelatedForeignKey: 'story_id',
  })
  declare stories: ManyToMany<typeof Story>

  @computed()
  public get price() {
    let price = 0
    if ((this.stories?.length ?? 0) > 0) {
      this.stories.forEach((story) => {
        if (!story.hasRedeemed) {
          price += story.price ?? 0
        }
      })

      price = price * (1 - (this.discount ?? 0))
    }

    return new BigNumber(price).decimalPlaces(2).toNumber()
  }
}