import { DateTime } from 'luxon'
import { BaseModel, column, computed, belongsTo, hasMany, manyToMany, hasOne } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany, HasOne, ManyToMany } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import StoryFilter from '#models/filters/story_filter'
import { generateUniqueSlug } from '../../migration-utils.js'
import Pack from './pack.js'
import Preschool from './preschool.js'
import Chapter from './chapter.js'
import StoryOrder from './story_order.js'
import Tag from './tag.js'
import Plan from './plan.js'
import BundleStory from './bundle_story.js'
import PurchaseClickTracker from './purchase_click_tracker.js'

export enum StoryType {
  COMMUNITY = 'community',
  GAME = 'game',
  PRESCHOOL = 'preschool',
  PACK = 'pack',
}

export default class Story extends compose(BaseModel, Filterable) {
  static $filter = () => StoryFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare handle: string

  @column()
  declare description: string | null

  @column()
  declare language: string

  // difficulty
  @column()
  declare level: number

  @column({ prepare: (value) => JSON.stringify(value) })
  declare region: string | Array<string>

  @column()
  declare thumbnailUrl: string | null

  @column()
  declare previewImageUrl: string | null

  @column()
  declare previewVideoId: number | null

  // break stories don't have options
  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isBreak: boolean

  @column()
  declare qrCode: string

  @column({ consume: (value: number) => Number(value) })
  declare price: number

  @column()
  declare compareAtPrice: number

  @column()
  declare wordCount: number

  @column()
  declare type: StoryType

  @column()
  declare isCommunity: boolean

  @column()
  declare communityReviewText: string

  @column()
  declare isFeatured: boolean

  @column()
  declare isExclusive: boolean

  @column()
  declare status: string

  @column()
  declare packId: number

  @column()
  declare preschoolId: number | null

  @column()
  declare ordering: number

  @column()
  declare defaultChapterId: number | null

  @column({ prepare: (value) => JSON.stringify(value) })
  declare metadata: any

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @manyToMany(() => Plan, {
    pivotTable: 'plan_stories',
    pivotForeignKey: 'story_id',
    pivotRelatedForeignKey: 'plan_id',
  })
  declare plans: ManyToMany<typeof Plan>

  @belongsTo(() => Pack)
  declare pack: BelongsTo<typeof Pack>

  @belongsTo(() => Preschool)
  declare preschool: BelongsTo<typeof Preschool>

  @hasMany(() => BundleStory)
  declare bundleStory: HasMany<typeof BundleStory>

  @hasMany(() => PurchaseClickTracker, { serializeAs: 'purchase_click_trackers' })
  declare purchaseClickTrackers: HasMany<typeof PurchaseClickTracker>

  @hasMany(() => Chapter)
  declare chapters: HasMany<typeof Chapter>

  @hasOne(() => Chapter)
  declare defaultChapter: HasOne<typeof Chapter>

  @hasMany(() => StoryOrder, { serializeAs: 'story_orders' })
  declare storyOrders: HasMany<typeof StoryOrder>

  @manyToMany(() => Tag, {
    pivotTable: 'story_tags',
    pivotTimestamps: true
  })
  declare tags: ManyToMany<typeof Tag>

  // Computed properties
  @computed({ serializeAs: 'has_redeemed' })
  get hasRedeemed() {
    return this.$extras.redeemed > 0
  }

  @computed({ serializeAs: 'purchase_clicks' })
  get clicks() {
    return this.$extras.clicks
  }

  @computed({ serializeAs: 'plan_id' })
  get planId() {
    return this.$extras.pivot_plan_id
  }

  @computed({ serializeAs: 'plan_level' })
  get planLevel() {
    return this.$extras.pivot_level
  }

  @computed({ serializeAs: 'plan_featured' })
  get planFeatured() {
    return this.$extras.pivot_is_featured
  }

  @computed({ serializeAs: 'plan_free' })
  get planFree() {
    return Boolean(this.$extras.pivot_is_free)
  }

  // Manual slug generation to replace @slugify decorator
  static async generateHandle(title: string): Promise<string> {
    const baseSlug = generateUniqueSlug(title)

    // Check if slug exists and increment if needed
    let finalSlug = baseSlug
    let counter = 1

    while (await Story.findBy('handle', finalSlug)) {
      finalSlug = `${baseSlug}-${counter}`
      counter++
    }

    return finalSlug
  }
}
