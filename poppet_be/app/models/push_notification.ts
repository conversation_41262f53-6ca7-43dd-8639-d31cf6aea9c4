import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class PushNotification extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare body: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare recipients: string | number[]

  @column()
  declare imageUrl: string

  @column()
  declare scheduled: boolean

  @column.dateTime()
  declare scheduledAt: DateTime

  @column()
  declare sent: boolean

  @column()
  declare status: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}