import { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import {
  BaseModel,
  column,
  beforeCreate,
  belongsTo,
  hasMany,
  hasOne,
  manyToMany
} from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { DbAccessTokensProvider } from '@adonisjs/auth/access_tokens'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import type { HasMany, BelongsTo, HasOne, ManyToMany } from '@adonisjs/lucid/types/relations'
import Admin from './admin.js'
import Child from './child.js'
import Device from './device.js'
import Subscription from './subscription.js'
import Transaction from './transaction.js'
import UserPack from './user_pack.js'
import Wallet from './wallet.js'
import { Filterable } from 'adonis-lucid-filter'
import PurchaseClickTracker from './purchase_click_tracker.js'
import Pack from './pack.js'
import StoryOrder from './story_order.js'

export const generateReferralCode = (length: number = 6) => {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 1; i <= length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email', 'username'],
  passwordColumnName: 'password',
})

export default class User extends compose(compose(BaseModel, AuthFinder), Filterable) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare stripeCustomerId: string

  @column()
  declare email: string

  @column()
  declare region: string

  @column({ serializeAs: null })
  declare password: string

  @column()
  declare referralCode: string

  @column()
  declare name: string

  @column()
  declare phone: string

  @column({ columnName: 'address_1', serializeAs: 'address_1' })
  declare address1: string

  @column({ columnName: 'address_2', serializeAs: 'address_2' })
  declare address2: string

  @column()
  declare postcode: string

  @column()
  declare rememberMeToken: string | null

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isAnonymous: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare verified?: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare phoneVerified?: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare blocked?: boolean

  @column()
  declare userGroupId: number | null

  @column()
  declare activeChildId: number

  @column.dateTime()
  declare promotionEndedAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null

  @hasMany(() => Child)
  declare children: HasMany<typeof Child>

  @manyToMany(() => Pack, {
    pivotTable: 'user_packs',
    pivotColumns: ['current_level'],
    pivotForeignKey: 'user_id',
    pivotRelatedForeignKey: 'pack_id',
    pivotTimestamps: true,
    serializeAs: 'packs',
  })
  declare userPacks: ManyToMany<typeof Pack>

  @hasMany(() => Transaction)
  declare transactions: HasMany<typeof Transaction>

  @hasMany(() => Subscription)
  declare subscriptions: HasMany<typeof Subscription>

  @hasMany(() => PurchaseClickTracker, { serializeAs: 'purchase_clicks' })
  declare purchaseClickTrackers: HasMany<typeof PurchaseClickTracker>

  @hasMany(() => Device)
  declare devices: HasMany<typeof Device>

  @belongsTo(() => Child, { foreignKey: 'activeChildId' })
  declare activeChild: BelongsTo<typeof Child>

  @belongsTo(() => Wallet)
  declare wallet: BelongsTo<typeof Wallet>

  @hasOne(() => Admin)
  declare admin: HasOne<typeof Admin>

  @hasMany(() => StoryOrder)
  declare storyOrders: HasMany<typeof StoryOrder>

  @beforeCreate()
  static async generateReferral(user: User) {
    if (!user.$dirty.referralCode) {
      let repeat = false
      let referralCode: string
      do {
        referralCode = generateReferralCode(8)
        if (await User.findBy('referral_code', referralCode)) {
          repeat = true
        } else {
          repeat = false
        }
      } while (repeat || !referralCode)
      user.referralCode = referralCode
    }
  }

  // Static methods
  static async verifyPassword(oldPassword: string, hashedPassword: string) {
    const checkPassword = await hash.verify(hashedPassword, oldPassword)
    return checkPassword
  }

  static accessTokens = DbAccessTokensProvider.forModel(User)
}
