import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import File from './file.js'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import SettingFilter from '#models/filters/setting_filter'

export default class Setting extends compose(BaseModel, Filterable) {
  static $filter = () => SettingFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare handle: string

  @column()
  declare value: string

  @column()
  declare status: string // active, inactive

  @column()
  declare description: string

  @column()
  declare type: string // system

  @column({ prepare: (values) => JSON.stringify(values) })
  declare metadata: any // json

  @column()
  declare fileId: number

  @belongsTo(() => File)
  declare file: BelongsTo<typeof File>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}