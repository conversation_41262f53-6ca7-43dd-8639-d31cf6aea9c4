import { DateTime } from 'luxon'
import { BaseModel, belongsTo, hasMany, column, computed } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'

import Story from './story.js'
import Preschool from './preschool.js'
import StoryOrder from './story_order.js'
import VoucherFilter from './filters/voucher_filter.js'

export default class Voucher extends compose(BaseModel, Filterable) {
  static $filter = () => VoucherFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare voucherCode: string

  @column()
  declare description: string

  @column()
  declare discount: number

  @column()
  declare maxOfUsed: number

  @column()
  declare storyId: number

  @belongsTo(() => Story)
  declare story: BelongsTo<typeof Story>

  @column()
  declare preschoolId: number

  @belongsTo(() => Preschool)
  declare preschool: BelongsTo<typeof Preschool>

  @hasMany(() => StoryOrder)
  declare storyOrders: HasMany<typeof StoryOrder>

  @column({ consume: (value: boolean) => Boolean(value) })
  declare blocked: boolean

  @column.dateTime()
  declare expiredAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @computed({ serializeAs: 'total_of_used' })
  public get totalOfUsed() {
    return this.$extras.total_of_used
  }
}
