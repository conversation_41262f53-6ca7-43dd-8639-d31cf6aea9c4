import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'

import Voucher from './voucher.js'
import Story from './story.js'
import User from './user.js'
import StoryOrderFilter from './filters/story_order_filter.js'

export type StoryStatus = {
  story_id: number
  child_id: number
  completed: boolean
}

export default class StoryOrder extends compose(BaseModel, Filterable) {
  static $filter = () => StoryOrderFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare voucherId: number

  @belongsTo(() => Voucher)
  declare voucher: BelongsTo<typeof Voucher>

  @column()
  declare storyId: number

  @belongsTo(() => Story)
  declare story: BelongsTo<typeof Story>

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column({ consume: (value: number) => Number(value) })
  declare finalAmount: number

  @column({ consume: (value: number) => Number(value) })
  declare discountAmount: number

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  declare storyStatuses: Array<StoryStatus>

  @column({ consume: (value: boolean) => Boolean(value) })
  declare blocked: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
