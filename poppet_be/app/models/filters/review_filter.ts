
import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type Review from '#models/review'

export default class ReviewFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Review>

  email(value: any): void {
    this.$query.whereHas('user', (query) => query.where('email', value))
  }

  fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}