
import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type Version from '#models/version'

export default class VersionFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Version>

  id(value: any): void {
    this.$query.where('id', value)
  }

  name(value: any): void {
    this.$query.where('name', 'LIKE', `%${value}%`)
  }

  platform(value: any): void {
    this.$query.where('platform', value)
  }
}