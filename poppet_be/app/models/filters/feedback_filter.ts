
import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type Feedback from '#models/feedback'

export default class FeedbackFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Feedback>

  message(value: any): void {
    this.$query.where('message', 'LIKE', `%${value}%`)
  }

  id(value: any): void {
    this.$query.where('id', value)
  }

  fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}