import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type Auth from '#models/auth'

export default class AuthFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Auth>

  public email(value: any): void {
    this.$query.where('email', 'LIKE', `%${value}%`)
  }

  public status(value: any): void {
    this.$query.where('status', value)
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }

  public id(value: any): void {
    this.$query.where('id', value)
  }
}
