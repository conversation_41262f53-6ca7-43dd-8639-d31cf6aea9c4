import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type Admin from '#models/admin'

export default class AdminFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Admin>

  public type(value: any): void {
    this.$query.where('type', value)
  }

  public id(value: any): void {
    this.$query.where('id', value)
  }
}
