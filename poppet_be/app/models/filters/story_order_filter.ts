import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type StoryOrder from '../story_order.js'

export default class StoryOrderFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof StoryOrder>

  public user(value: any): void {
    this.$query.where('user_id', value)
  }

  public preschool(value: any): void {
    this.$query.where('preschool_id', value)
  }
}
