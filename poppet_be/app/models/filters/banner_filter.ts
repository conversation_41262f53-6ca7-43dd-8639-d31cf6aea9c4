import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type Banner from '#models/banner'

export default class BannerFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Banner>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public region(value: any): void {
    this.$query.where('region', value)
  }
}
