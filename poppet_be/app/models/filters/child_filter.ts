
import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type Child from '#models/child'

export default class ChildFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Child>

  name(value: string): void {
    this.$query.where('name', value)
  }

  userId(value: any): void {
    this.$query.where('user_id', value)
  }
}