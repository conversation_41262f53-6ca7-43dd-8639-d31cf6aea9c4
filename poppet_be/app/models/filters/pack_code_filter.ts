
import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type PackCode from '#models/pack_code'

export default class PackCodeFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof PackCode>

  packCode(value: any): void {
    this.$query.where('pack_code', 'LIKE', `%${value}%`)
  }

  pack(value: any): void {
    this.$query.where('pack_id', value)
  }

  active(value: any): void {
    console.log(typeof value)
    if (value === '1') {
      this.$query.whereNull('user_id')
    } else if (value === '0') {
      this.$query.whereNotNull('user_id')
    }
  }
}