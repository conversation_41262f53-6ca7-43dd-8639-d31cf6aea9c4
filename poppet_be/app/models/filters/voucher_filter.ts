import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type Voucher from '#models/voucher'

export default class VoucherFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Voucher>

  public voucherCode(value: any): void {
    this.$query.where('voucher_code', 'LIKE', `%${value}%`)
  }

  public story(value: any): void {
    this.$query.where('story_id', Number(value))
  }
}
