import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type Plan from '#models/plan'

export default class PlanFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Plan>

  public title(value: any): void {
    this.$query.where('title', 'LIKE', `%${value}%`)
  }

  public region(value: any): void {
    this.$query.where('region', 'LIKE', `%${value}%`)
  }

  public language(value: any): void {
    this.$query.where('language', value)
  }

  public published(value: any): void {
    this.$query.where('published', value === 'true')
  }
}
