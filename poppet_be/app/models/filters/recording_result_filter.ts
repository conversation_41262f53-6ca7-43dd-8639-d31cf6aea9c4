import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type RecordingResult from '#models/recording_result'

export default class RecordingResultFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof RecordingResult>

  email(value: any): void {
    this.$query.whereHas('user', (query) => query.where('email', 'LIKE', `%${value}%`))
  }

  id(value: any): void {
    this.$query.where('id', value)
  }

  fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}
