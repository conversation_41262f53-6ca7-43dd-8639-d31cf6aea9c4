import { DateTime } from 'luxon'
import { BaseModel, column, hasMany } from '@adonisjs/lucid/orm'
import Story from './story.js'
import type { HasMany } from '@adonisjs/lucid/types/relations'

export default class Preschool extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare description: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare region: string | Array<string>

  @column()
  declare language: string

  @column({ consume: (value: boolean) => Boolean(value) })
  declare blocked: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @hasMany(() => Story)
  declare stories: HasMany<typeof Story>
}
