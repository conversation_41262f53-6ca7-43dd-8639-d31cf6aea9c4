import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import FeedbackFilter from './filters/feedback_filter.js'

export default class Feedback extends compose(BaseModel, Filterable) {
  static $filter = () => FeedbackFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare subject: string

  @column()
  declare content: string

  // to ensure correct user in reply form
  @column()
  declare token: string

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isClosed: boolean

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}