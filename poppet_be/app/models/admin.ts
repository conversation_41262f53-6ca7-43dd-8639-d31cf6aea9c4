import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import AdminFilter from '#models/filters/admin_filter'
import User from './user.js'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

export default class Admin extends compose(BaseModel, Filterable) {
  static $filter = () => AdminFilter
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  declare type: number

  //crud
  //0-r,1-rc,2-rcu,3-rcud
  declare accessKyc: number

  declare accessFunding: number

  declare accessUser: number

  declare accessReport: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User, {})
  declare user: BelongsTo<typeof User>
}
