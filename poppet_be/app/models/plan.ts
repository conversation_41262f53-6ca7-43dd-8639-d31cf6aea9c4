import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, manyToMany } from '@adonisjs/lucid/orm'
import type { ManyToMany, HasMany } from '@adonisjs/lucid/types/relations'
import Story from './story.js'
import Subscription from './subscription.js'
import PlanPricing from './plan_pricing.js'

export enum PlanType {
  COMMUNITY = 'community',
  GAME = 'game',
  PRESCHOOL = 'preschool',
  MUSIC = 'music',
}
export default class Plan extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare handle: string

  @column()
  declare title: string

  @column()
  declare description: string

  @column()
  declare type: PlanType

  @column()
  declare iconUrl: string | null

  @column()
  declare thumbnailUrl: string | null

  @column()
  declare stripeProductId: string | null

  @column({ prepare: (value) => JSON.stringify(value) })
  declare region: string | Array<string>

  @column()
  declare language: string

  @column({ consume: (value: boolean) => Boolean(value) })
  declare blocked: boolean

  // show in app home page if published
  @column({ consume: (value: boolean) => Boolean(value) })
  declare published: boolean

  @hasMany(() => PlanPricing)
  declare pricings: HasMany<typeof PlanPricing>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // for plans & subscriptions
  @manyToMany(() => Story, {
    pivotTable: 'plan_stories',
    pivotForeignKey: 'plan_id',
    pivotRelatedForeignKey: 'story_id',
    pivotColumns: ['level', 'is_featured', 'ordering', 'is_free'],
    pivotTimestamps: true,
    serializeAs: 'stories',
  })
  declare planStories: ManyToMany<typeof Story>

  @hasMany(() => Subscription)
  declare subscriptions: HasMany<typeof Subscription>
}
