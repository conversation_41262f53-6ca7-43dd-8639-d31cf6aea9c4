import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'

export default class FeedbackHistory extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare content: string

  @column()
  declare toUserId: number

  @belongsTo(() => User, { foreignKey: 'to_user_id' })
  declare toUser: BelongsTo<typeof User>

  @column()
  declare fromUserId: number

  @belongsTo(() => User, { foreignKey: 'from_user_id' })
  declare fromUser: BelongsTo<typeof User>

  @column()
  declare feedbackId: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}