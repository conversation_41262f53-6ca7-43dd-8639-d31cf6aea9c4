import { DateTime } from 'luxon'
import { BaseModel, column, computed, hasMany } from '@adonisjs/lucid/orm'
import type { HasMany } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import PackFilter from '#models/filters/pack_filter'
import Story from './story.js'
import PackLevel from './pack_level.js'
import UserPack from './user_pack.js'

export default class Pack extends compose(BaseModel, Filterable) {
  static $filter = () => PackFilter
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare description: string | null

  @column()
  declare thumbnailUrl: string | null

  @column()
  declare featuredImage: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare region: string | Array<string>

  @column()
  declare language: string

  @column()
  declare noOfLevel: number

  @column()
  declare store_url: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare slides: Array<object> | string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @hasMany(() => Story, {
    serializeAs: 'stories',
  })
  declare stories: HasMany<typeof Story>

  @hasMany(() => PackLevel, {
    serializeAs: 'packLevels',
  })
  declare packLevels: HasMany<typeof PackLevel>

  @hasMany(() => UserPack, {
    serializeAs: 'userPacks',
  })
  declare userPacks: HasMany<typeof UserPack>

  @computed({ serializeAs: 'has_redeemed' })
  public get hasRedeemed() {
    return this.$extras.redeemed > 0
  }
}
