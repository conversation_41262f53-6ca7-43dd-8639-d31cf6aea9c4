import { DateTime } from 'luxon'
import { BaseModel, column, computed, hasMany } from '@adonisjs/lucid/orm'
import type { HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'

// to group users to different country/market
export default class UserGroup extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  // one default group to assign to users
  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isDefault: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @hasMany(() => User)
  declare users: HasMany<typeof User>

  @computed({ serializeAs: 'users_count' })
  get usersCount() {
    if (this.$extras.users_count !== null) {
      return this.$extras.users_count
    }
    return null
  }
}
