import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, column } from '@adonisjs/lucid/orm'
import { snake } from 'radash'


export default class Credit extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare description: string

  @column()
  declare handle: string

  @column({ consume: (value: number) => Number(value) })
  declare amount: number

  @column({ consume: (value: number) => Number(value) })
  declare price: number // current price

  @column({ consume: (value: number) => Number(value) })
  declare compareAt: number // original price

  @column()
  declare currency: string

  @column()
  declare rank: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @beforeCreate()
  static updateField(credit: Credit) {
    if (credit.$dirty.handle === null) {
      credit.handle = snake(`top-up ${credit.amount} ${credit.currency}`)
    }
  }
}
