import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, beforeUpdate, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Story from './story.js'
import User from './user.js'

export default class PurchaseClickTracker extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare storyId: number

  @column()
  declare clicks: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Story)
  declare story: BelongsTo<typeof Story>

  @beforeCreate()
  static async createClick(entry: PurchaseClickTracker) {
    if (!entry.$dirty.clicks) {
      entry.clicks = 1
    }
  }

  @beforeUpdate()
  static async updateClick(entry: PurchaseClickTracker) {
    if (!entry.$dirty.clicks) {
      entry.clicks = entry.clicks + 1
    }
  }

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}