import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import File from './file.js'
import Pack from './pack.js'

export type RiveInput = {
  key: string
  type: string
  story_id?: number
  value?: boolean
}

export default class PackLevel extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare level: number

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  declare inputs: Array<RiveInput>
  // db: [({ key: 'outdoor', type: 'boolean', story_id: 1 }, { key: 'bedroom', type: 'boolean', story_id: 2 })]
  // computed: [{ key: 'outdoor', type: 'boolean', story_id: 1, value: true/false }, { key: 'bedroom', type: 'boolean' }]

  @column()
  declare fileId: number

  @belongsTo(() => File, { foreignKey: 'fileId' })
  declare file: BelongsTo<typeof File>

  @column()
  declare audioUrl: string

  @column()
  declare packId: number

  @belongsTo(() => Pack, { foreignKey: 'packId' })
  declare pack: BelongsTo<typeof Pack>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
