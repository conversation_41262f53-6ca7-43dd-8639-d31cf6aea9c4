import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Plan from './plan.js'
import User from './user.js'
import PlanPricing from './plan_pricing.js'

export default class Subscription extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare planId: number

  @belongsTo(() => Plan)
  declare plan: BelongsTo<typeof Plan>

  @column()
  declare planPricingId: number

  @belongsTo(() => PlanPricing)
  declare planPricing: BelongsTo<typeof PlanPricing>

  @column()
  declare provider: string
  // google
  // apple
  // stripe

  @column()
  declare providerPlanId: string

  @column()
  declare providerSubscriptionId: string

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column()
  declare providerUserId: string

  @column.dateTime()
  declare startDate: DateTime
  // stripe: current_period_start

  @column.dateTime()
  declare endDate: DateTime | null | undefined
  // actual end date
  // TODO: on ended webhook from stripe

  @column.dateTime()
  declare cycleStartDate: DateTime
  // stripe: current_period_start

  @column.dateTime()
  declare cycleEndDate: DateTime
  // stripe: current_period_end

  @column()
  declare status: string
  // stripe status: incomplete, trialing, active, past_due, canceled, unpaid

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
