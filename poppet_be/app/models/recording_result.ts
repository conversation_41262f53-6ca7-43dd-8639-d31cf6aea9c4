import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Chapter from './chapter.js'
import User from './user.js'
import Session from './session.js'
import Story from './story.js'
import File from './file.js'

export default class RecordingResult extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  // text to compare
  @column()
  declare category: string

  // pinyin: extra text, same sound but different word
  @column({ prepare: (value) => JSON.stringify(value) })
  declare extraText: string

  // pinyin: text compared and remain
  @column({ prepare: (value) => JSON.stringify(value) })
  declare remainingText: string

  @column()
  declare pinyinHypothesis: string

  @column()
  declare hypothesis: string

  @column()
  declare hypothesisScore: number

  @column()
  declare language: string

  @column()
  declare device: string

  @column()
  declare minimumVolume: string

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  declare rawResult: object // json

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  declare calibrations: string[] // json

  @column({
    prepare: (value: object) => JSON.stringify(value),
  })
  declare volumes: string[] // json

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isRepeat: boolean

  @column()
  declare chapterId: number

  @belongsTo(() => Chapter)
  declare chapter: BelongsTo<typeof Chapter>

  @column()
  declare storyId: number

  @belongsTo(() => Story)
  declare story: BelongsTo<typeof Story>

  @column()
  declare sessionId: number

  @belongsTo(() => Session)
  declare session: BelongsTo<typeof Session>

  @column()
  declare fileId: number

  @belongsTo(() => File)
  declare file: BelongsTo<typeof File>

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

}
