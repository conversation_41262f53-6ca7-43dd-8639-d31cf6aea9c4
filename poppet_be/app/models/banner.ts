import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import BannerFilter from './filters/banner_filter.js'

export default class Banner extends compose(BaseModel, Filterable)  {
  static $filter = () => BannerFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare thumbnailUrl: string

  @column()
  declare ctaUrl: string

  @column()
  declare region: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
