import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import Plan from './plan.js'
import Subscription from './subscription.js'

export default class PlanPricing extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare planId: number

  @column()
  declare appstoreProductId: string

  @column()
  declare playstoreProductId: string

  @column()
  declare stripePriceId: string | null

  @column()
  declare duration: 'month' | 'year' | undefined

  @column({ consume: (value: number) => Number(value) })
  declare price: number

  @column({ consume: (value: boolean) => Boolean(value) })
  declare blocked: boolean

  @column({ consume: (value: number) => Number(value) })
  declare discount: number // in percentage

  @column.dateTime()
  declare promotionEndedAt: DateTime

  @belongsTo(() => Plan)
  declare plan: BelongsTo<typeof Plan>

  @hasMany(() => Subscription)
  declare subscriptions: HasMany<typeof Subscription>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}