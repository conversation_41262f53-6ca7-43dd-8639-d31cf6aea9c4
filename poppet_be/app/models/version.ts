import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import VersionFilter from './filters/version_filter.js'

export default class Version extends compose(BaseModel, Filterable) {
  static $filter = () => VersionFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare platform: string

  @column()
  declare buildVersion: string

  @column()
  declare buildNumber: number

  @column()
  declare releaseNote: string

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare update: boolean

  @column()
  declare appUrl: string

  @column.dateTime()
  declare publishedAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}