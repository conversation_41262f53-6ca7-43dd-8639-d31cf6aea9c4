import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Story from './story.js'
import User from './user.js'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import ReviewFilter from '#models/filters/review_filter'

export default class Review extends compose(BaseModel, Filterable) {
  static $filter = () => ReviewFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare content: string

  @column()
  declare score: number

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare moreActivity: boolean

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column()
  declare storyId: number

  @belongsTo(() => Story)
  declare story: BelongsTo<typeof Story>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}