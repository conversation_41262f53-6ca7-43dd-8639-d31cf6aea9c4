import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Pack from './pack.js'
import User from './user.js'
import UserGroup from './user_group.js'
import { Filterable } from 'adonis-lucid-filter'
import { compose } from '@adonisjs/core/helpers'
import PackCodeFilter from './filters/pack_code_filter.js'

export default class PackCode extends compose(BaseModel, Filterable) {
  static $filter = () => PackCodeFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare packId: number

  @belongsTo(() => Pack)
  declare pack: BelongsTo<typeof Pack>

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column()
  declare userGroupId: number

  @belongsTo(() => UserGroup)
  declare userGroup: BelongsTo<typeof UserGroup>

  @column()
  declare packCode: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
