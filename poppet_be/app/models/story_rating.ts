import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Story from './story.js'
import User from './user.js'

export enum StoryRatingStatus {
  ASKING = 'asking',
  PENDING = 'pending',
  RATED = 'rated',
}

type RatingReason = {
  selected_options?: string[]
  others?: string
}

export default class StoryRating extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column()
  declare storyId: number

  @belongsTo(() => Story)
  declare story: BelongsTo<typeof Story>

  @column()
  declare rating: number

  @column()
  declare review: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare reason: string | RatingReason

  @column()
  declare count: number

  @column()
  declare status: StoryRatingStatus

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}