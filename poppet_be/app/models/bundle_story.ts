import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Bundle from './bundle.js'
import Story from './story.js'

export default class BundleStory extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare bundleId: number

  @belongsTo(() => Bundle)
  declare bundle: BelongsTo<typeof Bundle>

  @column()
  declare storyId: number

  @belongsTo(() => Story)
  declare story: BelongsTo<typeof Story>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}