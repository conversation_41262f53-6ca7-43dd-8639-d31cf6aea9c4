import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, computed, hasMany } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import Session from './session.js'
import ChildFilter from './filters/child_filter.js'

export default class Child extends compose(BaseModel, Filterable) {
  static $filter = () => ChildFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column.dateTime({
    prepare: (value: Date | DateTime) => {
      if (value instanceof Date) {
        return DateTime.fromJSDate(value)
      }
      return value
    }
  })
  declare birthdate: DateTime

  @column()
  declare englishLevel: number

  @column()
  declare chineseLevel: number

  @column()
  declare userId: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @hasMany(() => Session)
  declare sessions: HasMany<typeof Session>

  @computed({ serializeAs: 'attempts_to_speak' })
  get attemptsToSpeak() {
    if (!this.sessions) {
      return 0
    }

    return this.sessions.reduce((accum, cur) => accum + (cur?.recordingResults?.length ?? 0), 0)
  }

  @computed({ serializeAs: 'words_count' })
  get wordsCount() {
    return this.$extras.wordsCount ?? 0
  }
}
