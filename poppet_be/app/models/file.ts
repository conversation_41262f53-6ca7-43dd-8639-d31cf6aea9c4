import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import User from './user.js'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

export default class File extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare position: number

  @column()
  declare alt: string

  @column()
  declare width: number

  @column()
  declare height: number

  @column()
  declare src: string | null

  @column()
  declare thumbnailUrl: string

  @column()
  declare type: string
  // file, image, video, recording

  @column()
  declare ext: string

  // @column()
  // declare fileName: string

  // @column()
  // declare originalName: string

  // @column()
  // declare mimeType: string

  // @column()
  // declare size: number

  // @column()
  // declare path: string

  // @column()
  // declare url: string

  // @column({ prepare: (value) => JSON.stringify(value) })
  // declare metadata: Record<string, unknown>

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

}
