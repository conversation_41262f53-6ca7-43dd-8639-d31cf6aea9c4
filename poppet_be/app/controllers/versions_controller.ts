import type { HttpContext } from '@adonisjs/core/http'
import Version from '#models/version'
import {
  createVersionValidator,
  updateVersionValidator,
  findLatestVersionValidator,
} from '#validators/utility_validator'

export default class VersionsController {
  /**
   * Create a new version
   */
  async create({ request, response }: HttpContext) {
    const validationData = await request.validateUsing(createVersionValidator)

    const createVersion = await Version.create({
      ...validationData,
    })

    return response.status(200).send({
      success: true,
      data: createVersion,
    })
  }

  /**
   * Update an existing version
   */
  async update({ request, response, params }: HttpContext) {
    const validationData = await request.validateUsing(updateVersionValidator)
    console.log(validationData)

    const version = await Version.findByOrFail('id', params.id)
    version.merge({ ...validationData })
    await version.save()

    return response.status(200).send({
      success: true,
      data: version,
    })
  }

  /**
   * Delete a version
   */
  async delete({ params, response }: HttpContext) {
    const version = await Version.findByOrFail('id', params.id)
    await version.delete()

    return response.status(200).send({ success: true })
  }

  /**
   * Get all versions with pagination
   */
  async find({ response, request }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const versions = await Version.query()
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.send(versions)
  }

  /**
   * Get a specific version
   */
  async findOne({ request, response, params }: HttpContext) {
    const version = await Version.filter(request.all())
      .where('id', params.id)
      .first()

    return response.send({
      data: version,
    })
  }

  /**
   * Get latest version for a platform
   */
  async findLatestVersionPlatform({ response, request }: HttpContext) {
    const validationData = await request.validateUsing(findLatestVersionValidator)

    const version = await Version.query()
      .where('platform', validationData.platform)
      .orderBy('published_at', 'desc')
      .first()

    return response.send({
      data: version,
    })
  }
}
