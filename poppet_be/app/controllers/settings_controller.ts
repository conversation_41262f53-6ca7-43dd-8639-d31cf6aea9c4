import type { HttpContext } from '@adonisjs/core/http'
import Setting from '#models/setting'
import { camelCase } from '@poppinss/utils/build/src/Helpers/string'
import { createSettingValidator, updateSettingValidator } from '#validators/utility_validator'
import { omit } from 'radash'

export default class SettingsController {
  /**
   * Create a new setting
   */
  async create({ request, response }: HttpContext) {
    const validateData = await request.validateUsing(createSettingValidator)

    const createSetting = await Setting.create({
      name: validateData.name,
      handle: validateData.handle,
      description: validateData.description,
      status: validateData.status,
      value: validateData.value,
      type: validateData.type,
    })

    return response.status(200).send({ success: true, data: createSetting })
  }

  /**
   * Update an existing setting
   */
  async update({ request, response, params }: HttpContext) {
    const validateData = await request.validateUsing(updateSettingValidator)

    const updateSetting = await Setting.findOrFail(params.id)
    for (const key of Object.keys(validateData)) {
      updateSetting[camelCase(key)] = validateData[key]
    }
    await updateSetting.save()

    return response.status(200).send({ success: true, data: updateSetting })
  }

  /**
   * Delete a setting
   */
  async deleteSetting({ params, response }: HttpContext) {
    const deleteSetting = await Setting.findOrFail(params.id)
    await deleteSetting.delete()

    return response.status(200).send({ success: true })
  }

  /**
   * Get all settings with filtering and pagination
   */
  async findAll({ response, request }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = omit(request.all(), ['page', 'limit', 'sort'])

    const allSetting = await Setting.filter(filters)
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(allSetting)
  }

  /**
   * Find setting by handle
   */
  async findByHandle({ response, params }: HttpContext) {
    const setting = await Setting.query()
      .where('handle', params.handle)
      .where('status', 'active')
      .first()

    return response.status(200).send({ data: setting })
  }
}
