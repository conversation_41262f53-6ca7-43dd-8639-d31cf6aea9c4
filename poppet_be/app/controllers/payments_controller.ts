import { androidpublisher, auth } from '@googleapis/androidpublisher'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import vine from '@vinejs/vine'
import BigNumber from 'bignumber.js'
import { DateTime } from 'luxon'

import Preschool from '#models/preschool'
import Story from '#models/story'
import StoryOrder from '#models/story_order'
import Voucher from '#models/voucher'
import Wallet from '#models/wallet'

const getAndroidPublisher = async () => {
  const auth2 = new auth.GoogleAuth({
    keyFile: process.env.GOOGLE_IAP,
    scopes: ['https://www.googleapis.com/auth/androidpublisher'],
  })
  const androidPublisher = androidpublisher({ auth: auth2, version: 'v3' })
  return androidPublisher
}

export default class PaymentsController {
  /**
   * Process waived purchase (free with voucher)
   */
  public async waivedPurchase({ request, response, auth }: HttpContext) {
    try {
      const user = await auth.authenticate()

      const waivedPurchaseValidator = vine.compile(
        vine.object({
          story_id: vine.number().optional(),
          preschool_id: vine.number().optional(),
          voucher_id: vine.number().optional(),
        })
      )

      const validationData = await request.validateUsing(waivedPurchaseValidator)

      // Validate that either story_id or preschool_id is provided
      if (!validationData.story_id && !validationData.preschool_id) {
        return response.status(400).send({
          success: false,
          message: 'Either story_id or preschool_id is required',
        })
      }

      let findVoucher: Voucher | null = null
      const findStory = await Story.find(validationData.story_id ?? 0)
      const findPreschool = await Preschool.find(validationData.preschool_id ?? 0)

      if (validationData.voucher_id) {
        findVoucher = await Voucher.query()
          .where('id', validationData.voucher_id)
          .where((query) => {
            query
              .whereNull('expired_at')
              .orWhere('expired_at', '>=', DateTime.local().toFormat('yyyy-MM-dd HH:mm:ss'))
          })
          .withCount('storyOrders', (query) => {
            query.countDistinct('user_id').as('total_of_used')
          })
          .first()

        if (!findVoucher) {
          return response.status(400).send({
            success: false,
            message: 'Invalid voucher or voucher expired.',
          })
        }

        if (findVoucher.maxOfUsed !== null) {
          if (findVoucher.totalOfUsed >= findVoucher.maxOfUsed) {
            return response.status(400).send({
              success: false,
              message: 'The voucher usage limit has been reached.',
            })
          }
        }

        const purchaseFee = new BigNumber(findStory?.price ?? 0)
          .multipliedBy(1 - findVoucher.discount)
          .decimalPlaces(2)
          .toNumber()

        if (purchaseFee !== 0) {
          return response.status(400).send({
            success: false,
            message: 'Please proceed with in-app-purchase',
          })
        }
      } else {
        if (findStory && findStory.price > 0) {
          return response.status(400).send({
            success: false,
            message: 'Please proceed with in-app-purchase',
          })
        }
      }

      // Process the waived purchase
      const result = await db.transaction(async (trx) => {
        const newStoryOrder = new StoryOrder()
        if (findStory) {
          newStoryOrder.storyId = findStory.id
          newStoryOrder.price = 0
        }
        if (findPreschool) {
          newStoryOrder.preschoolId = findPreschool.id
          newStoryOrder.price = 0
        }
        if (findVoucher) {
          newStoryOrder.voucherId = findVoucher.id
        }
        newStoryOrder.userId = user.id
        newStoryOrder.blocked = false

        newStoryOrder.useTransaction(trx)
        await newStoryOrder.save()

        return newStoryOrder
      })

      return response.status(200).send({
        success: true,
        data: result,
      })
    } catch (error) {
      logger.error('Payments waivedPurchase error: %o', error)
      return response.status(400).send({
        success: false,
        message: 'Failed to process waived purchase',
      })
    }
  }

  /**
   * Verify Google Play purchase
   */
  public async validatePurchaseAndroid({ request, response, auth }: HttpContext) {
    try {
      const user = await auth.authenticate()

      const verifyGooglePlayValidator = vine.compile(
        vine.object({
          purchase_token: vine.string(),
          product_id: vine.string(),
          package_name: vine.string(),
        })
      )

      const validationData = await request.validateUsing(verifyGooglePlayValidator)

      const androidPublisher = await getAndroidPublisher()

      const result = await androidPublisher.purchases.products.get({
        packageName: validationData.package_name,
        productId: validationData.product_id,
        token: validationData.purchase_token,
      })

      if (result.data.purchaseState !== 0) {
        return response.status(400).send({
          success: false,
          message: 'Purchase not completed',
        })
      }

      // Process the verified purchase
      // Add your purchase processing logic here

      return response.status(200).send({
        success: true,
        data: result.data,
      })
    } catch (error) {
      logger.error('Payments verifyGooglePlayPurchase error: %o', error)
      return response.status(400).send({
        success: false,
        message: 'Failed to verify Google Play purchase',
      })
    }
  }

  /**
   * Verify Apple App Store purchase
   */
  public async verifyAppleAppStorePurchase({ request, response, auth }: HttpContext) {
    try {
      const user = await auth.authenticate()

      const verifyAppleValidator = vine.compile(
        vine.object({
          receipt_data: vine.string(),
          password: vine.string().optional(),
        })
      )

      const validationData = await request.validateUsing(verifyAppleValidator)

      const verifyReceiptUrl =
        process.env.APPLE_VERIFY_RECEIPT_URL || 'https://buy.itunes.apple.com/verifyReceipt'

      const response_data = await axios.post(verifyReceiptUrl, {
        'receipt-data': validationData.receipt_data,
        'password': validationData.password || process.env.APPLE_SHARED_SECRET,
      })

      if (response_data.data.status !== 0) {
        return response.status(400).send({
          success: false,
          message: 'Invalid receipt',
        })
      }

      // Process the verified purchase
      // Add your purchase processing logic here

      return response.status(200).send({
        success: true,
        data: response_data.data,
      })
    } catch (error) {
      logger.error('Payments verifyAppleAppStorePurchase error: %o', error)
      return response.status(400).send({
        success: false,
        message: 'Failed to verify Apple App Store purchase',
      })
    }
  }
}
