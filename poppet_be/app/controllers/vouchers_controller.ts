import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import { omit } from 'radash'
import { DateTime } from 'luxon'
import Randomstring from 'randomstring'
import BigNumber from 'bignumber.js'

import Voucher from '#models/voucher'
import Story from '#models/story'
import Preschool from '#models/preschool'
import { createVoucherValidator, verifyVoucherValidator } from '#validators/voucher_validator'

export default class VouchersController {
  /**
   * Get all vouchers with filtering and pagination
   */
  public async findAll({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'sort', 'limit'])

      const voucherCodes = await Voucher.filter(filters)
        .preload('story')
        .preload('preschool')
        .preload('storyOrders')
        .withCount('storyOrders', (query) => query.countDistinct('user_id').as('total_of_used'))
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(voucherCodes)
    } catch (error) {
      logger.error('Vouchers findAll error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Create a new voucher
   */
  public async create({ request, response }: HttpContext) {
    try {
      const validationData = await request.validateUsing(createVoucherValidator)

      if (validationData.code) {
        const repeatedCode = await Voucher.findBy('code', validationData.code)
        if (repeatedCode) {
          return response.status(400).send({
            success: false,
            message: 'The code already exists.',
          })
        }
      }

      let uid: string = validationData.code ?? ''
      let isUnique = true
      while (uid === '' || !isUnique) {
        uid = Randomstring.generate(6)
        const repeatedCode = await Voucher.findBy('code', uid)
        if (repeatedCode) {
          isUnique = false
        } else {
          isUnique = true
        }
      }

      const result = await db.transaction(async (trx) => {
        const newVoucher = new Voucher()
        newVoucher.code = uid
        if (validationData.description) {
          newVoucher.description = validationData.description
        }
        if (validationData.story_id) {
          newVoucher.storyId = validationData.story_id
        } else if (validationData.preschool_id) {
          newVoucher.preschoolId = validationData.preschool_id
        }

        if (validationData.max_of_used) {
          newVoucher.maxUses = validationData.max_of_used
        }

        if (validationData.expired_at) {
          const utc = validationData.expired_at
          const gmt8 = utc.setZone('Asia/Kuala_lumpur').set({
            hour: 23,
            minute: 59,
            second: 59,
          })
          newVoucher.expiresAt = gmt8.toLocal()
        }
        newVoucher.discountValue = validationData.discount

        newVoucher.useTransaction(trx)
        await newVoucher.save()

        return newVoucher
      })

      return response.status(200).send({ success: true, data: result })
    } catch (error) {
      logger.error('Voucher create error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Verify a voucher code
   */
  public async verify({ request, response }: HttpContext) {
    try {
      const validationData = await request.validateUsing(verifyVoucherValidator)

      const findStory = await Story.find(validationData.story_id ?? 0)
      const findPreschool = await Preschool.find(validationData.preschool_id ?? 0)

      const voucher = await Voucher.query()
        .where('code', validationData.code)
        .where((query) => {
          query
            .whereNull('expired_at')
            .orWhere('expired_at', '>=', DateTime.local().toFormat('yyyy-MM-dd HH:mm:ss'))
        })
        .first()

      if (
        !voucher ||
        (voucher.storyId !== findStory?.id && voucher.preschoolId !== findPreschool?.id)
      ) {
        return response.status(400).send({
          success: false,
          message: 'Invalid code.',
        })
      }

      if (findStory && (!findStory?.price || (findStory?.price ?? 0) <= 0)) {
        return response.status(400).send({
          success: false,
          message: 'Invalid price',
        })
      }

      return response.status(200).send({
        success: true,
        data: {
          ...(findStory && {
            story_id: findStory.id,
            price: findStory.price,
            discount: voucher.discountValue,
            final_price: new BigNumber(findStory.price)
              .multipliedBy(1 - voucher.discountValue)
              .decimalPlaces(2)
              .toNumber(),
          }),
          ...(findPreschool && { preschool_id: findPreschool.id }),
          voucher_id: voucher.id,
        },
      })
    } catch (error) {
      logger.error('Voucher verify error: %o', error)
      return response.status(400).send(error)
    }
  }
}
