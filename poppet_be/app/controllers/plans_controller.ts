import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import vine from '@vinejs/vine'
import { omit } from 'radash'
import stripe from 'stripe'

import Plan from '#models/plan'
import Subscription from '#models/subscription'
import User from '#models/user'
import { uploadToS3Bucket } from './files_controller.js'

export default class PlansController {
  /**
   * Get Stripe products
   */
  public async findStripeProducts({ response }: HttpContext) {
    try {
      const stripeInstance = new stripe(process.env.STRIPE_SECRET_KEY!)

      const products = await stripeInstance.products.list({
        active: true,
        expand: ['data.default_price'],
      })

      // remove products with non-recurring prices
      const plans = products.data.filter(
        (product) => (product.default_price as stripe.Price).type === 'recurring'
      )

      return response.ok({ data: plans })
    } catch (error) {
      logger.error('Plans findStripeProducts error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Create Stripe checkout session
   */
  public async createStripeCheckoutSession({ auth, request, response }: HttpContext) {
    try {
      const user = await User.findOrFail(auth?.user?.id)

      const createCheckoutValidator = vine.compile(
        vine.object({
          price_id: vine.string(),
          success_url: vine.string(),
          cancel_url: vine.string(),
        })
      )

      const validationData = await request.validateUsing(createCheckoutValidator)
      const stripeInstance = new stripe(process.env.STRIPE_SECRET_KEY!)

      const params: stripe.Checkout.SessionCreateParams = {
        mode: 'subscription',
        payment_method_types: ['card'],
        line_items: [
          {
            price: validationData.price_id,
            quantity: 1,
          },
        ],
        success_url: validationData.success_url,
        cancel_url: validationData.cancel_url,
        customer_email: user.email,
        metadata: {
          user_id: user.id.toString(),
        },
      }

      const session = await stripeInstance.checkout.sessions.create(params)

      return response.ok({ success: true, data: session })
    } catch (error) {
      logger.error('Plans createStripeCheckoutSession error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get all plans with filtering and pagination
   */
  public async findPlans({ auth, request, response }: HttpContext) {
    try {
      const user = await User.find(auth?.user?.id ?? 0)
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      if (user === null) {
        const plans = await Plan.query()
          .where('published', true)
          .preload('pricings')
          .preload('planStories')
          .orderBy(sort[0], sort[1])
          .paginate(page, limit)

        return response.ok(plans)
      }

      const plans = await Plan.query()
        .where('published', true)
        .preload('pricings')
        .preload('planStories')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(plans)
    } catch (error) {
      logger.error('Plans findPlans error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get a single plan by ID
   */
  public async findOnePlan({ auth, params, response }: HttpContext) {
    try {
      const user = await User.find(auth?.user?.id ?? 0)

      if (user === null) {
        const plan = await Plan.query()
          .where('id', params.id)
          .preload('pricings')
          .preload('planStories', (query) => query.orderBy('pivot_ordering', 'asc'))
          .first()

        return response.ok({ data: plan })
      }

      const plan = await Plan.query()
        .where('id', params.id)
        .preload('pricings')
        .preload('planStories', (query) =>
          query
            .withCount('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false).as('redeemed')
            )
            .preload('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false)
            )
            .orderBy('pivot_ordering', 'asc')
        )
        .first()

      return response.ok({ data: plan })
    } catch (error) {
      logger.error('Plans findOnePlan error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get a plan by handle
   */
  public async findPlanByHandle({ auth, params, response }: HttpContext) {
    try {
      const user = await User.find(auth?.user?.id ?? 0)

      if (user === null) {
        const plan = await Plan.query()
          .where('handle', params.handle)
          .preload('pricings')
          .preload('planStories', (query) => query.orderBy('pivot_ordering', 'asc'))
          .first()

        return response.ok({ data: plan })
      }

      const plan = await Plan.query()
        .where('handle', params.handle)
        .preload('pricings')
        .preload('planStories', (query) =>
          query
            .withCount('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false).as('redeemed')
            )
            .preload('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false)
            )
            .orderBy('pivot_ordering', 'asc')
        )
        .first()

      return response.ok({ data: plan })
    } catch (error) {
      logger.error('Plans findPlanByHandle error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get user subscriptions
   */
  public async findMySubscriptions({ auth, request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')

      const user = await auth.authenticate()
      const subscriptions = await Subscription.query()
        .where('user_id', user.id)
        .whereIn('status', ['trailing', 'active'])
        .whereNull('end_date')
        .preload('plan', (query) => {
          query.preload('planStories', (query) =>
            query
              .withCount('storyOrders', (query) =>
                query.where('user_id', user.id).where('blocked', false).as('redeemed')
              )
              .preload('storyOrders', (query) =>
                query.where('user_id', user.id).where('blocked', false)
              )
              .orderBy('pivot_ordering', 'asc')
          )
        })
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(subscriptions)
    } catch (error) {
      logger.error('Plans findMySubscriptions error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get stories for a plan
   */
  public async findPlanStories({ auth, params, request, response }: HttpContext) {
    try {
      const user = await User.find(auth?.user?.id ?? 0)
      const plan = await Plan.findOrFail(params.id)

      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'pivot_ordering:asc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])
      const featured = request.input('featured')

      if (user === null) {
        const stories = await plan
          .related('planStories')
          .query()
          .if(filters.level, (query) => {
            query.where('stories.level', filters.level)
          })
          .if(filters.tags, (query) => {
            query.whereHas('tags', (tagsQuery) => {
              tagsQuery.whereIn(
                'tags.id',
                Array.isArray(filters.tags) ? filters.tags : [filters.tags]
              )
            })
          })
          .if(filters.title, (query) => {
            query.where('title', 'LIKE', `%${filters.title}%`)
          })
          .if(featured, (query) => {
            query.wherePivot('is_featured', true)
          })
          .orderBy(sort[0], sort[1])
          .paginate(page, limit)

        return response.ok(
          stories.serialize({
            fields: {
              omit: ['ordering', 'is_featured'],
            },
          })
        )
      }

      const stories = await plan
        .related('planStories')
        .query()
        .if(filters.level, (query) => {
          query.where('stories.level', filters.level)
        })
        .if(filters.tags, (query) => {
          query.whereHas('tags', (tagsQuery) => {
            tagsQuery.whereIn(
              'tags.id',
              Array.isArray(filters.tags) ? filters.tags : [filters.tags]
            )
          })
        })
        .if(filters.title, (query) => {
          query.where('title', 'LIKE', `%${filters.title}%`)
        })
        .if(featured, (query) => {
          query.wherePivot('is_featured', true)
        })
        .withCount('storyOrders', (query) =>
          query.where('user_id', user.id).where('blocked', false).as('redeemed')
        )
        .preload('storyOrders', (query) => query.where('user_id', user.id).where('blocked', false))
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(
        stories.serialize({
          fields: {
            omit: ['ordering', 'is_featured'],
          },
        })
      )
    } catch (error) {
      logger.error('Plans findPlanStories error: %o', error)
      return response.badRequest(error)
    }
  }

  // ------------------------------
  // Admin methods
  // ------------------------------

  /**
   * Get all plans (admin endpoint)
   */
  public async adminFind({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      const plans = await Plan.query()
        .preload('pricings')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(plans)
    } catch (error) {
      logger.error('Plans adminFind error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get a single plan (admin endpoint)
   */
  public async adminFindOne({ params, response }: HttpContext) {
    try {
      const plan = await Plan.query()
        .where('id', params.id)
        .preload('pricings')
        .preload('planStories', (query) => query.orderBy('pivot_ordering', 'asc'))
        .first()

      return response.ok({ data: plan })
    } catch (error) {
      logger.error('Plans adminFindOne error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Create a new plan (admin endpoint)
   */
  public async create({ auth, request, response }: HttpContext) {
    try {
      const user = await User.findOrFail(auth?.user?.id)

      const createPlanValidator = vine.compile(
        vine.object({
          title: vine.string(),
          handle: vine.string(),
          description: vine.string(),
          region: vine.array(vine.string()),
          language: vine.string(),
          blocked: vine.boolean().optional(),
          published: vine.boolean().optional(),
          thumbnail: vine
            .file({
              extnames: ['png', 'jpg', 'jpeg'],
              size: '20mb',
            })
            .optional(),
        })
      )

      const validationData = await request.validateUsing(createPlanValidator)

      const result = await db.transaction(async (trx) => {
        const newPlan = new Plan()
        newPlan.title = validationData.title
        newPlan.handle = validationData.handle
        newPlan.description = validationData.description
        newPlan.region = validationData.region
        newPlan.language = validationData.language
        newPlan.blocked = validationData.blocked ?? false
        newPlan.published = validationData.published ?? false

        if (validationData.thumbnail) {
          const uploadBucket = await uploadToS3Bucket(
            validationData.thumbnail,
            process.env.S3_BUCKET ?? 'hummusedu',
            user.email
          )

          if (uploadBucket?.url) {
            newPlan.thumbnailUrl = uploadBucket.url
          }
        }

        newPlan.useTransaction(trx)
        await newPlan.save()

        return newPlan
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      logger.error('Plans create error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Update an existing plan (admin endpoint)
   */
  public async update({ auth, request, response, params }: HttpContext) {
    try {
      const user = await User.findOrFail(auth?.user?.id)
      const plan = await Plan.findOrFail(params.id)

      const updatePlanValidator = vine.compile(
        vine.object({
          title: vine.string().optional(),
          handle: vine.string().optional(),
          description: vine.string().optional(),
          region: vine.array(vine.string()).optional(),
          language: vine.string().optional(),
          blocked: vine.boolean().optional(),
          published: vine.boolean().optional(),
          thumbnail: vine
            .file({
              extnames: ['png', 'jpg', 'jpeg'],
              size: '20mb',
            })
            .optional(),
        })
      )

      const validationData = await request.validateUsing(updatePlanValidator)

      const result = await db.transaction(async (trx) => {
        if (validationData.title) plan.title = validationData.title
        if (validationData.handle) plan.handle = validationData.handle
        if (validationData.description) plan.description = validationData.description
        if (validationData.region) plan.region = validationData.region
        if (validationData.language) plan.language = validationData.language
        if (validationData.blocked !== undefined) plan.blocked = validationData.blocked
        if (validationData.published !== undefined) plan.published = validationData.published

        if (validationData.thumbnail) {
          const uploadBucket = await uploadToS3Bucket(
            validationData.thumbnail,
            process.env.S3_BUCKET ?? 'hummusedu',
            user.email
          )

          if (uploadBucket?.url) {
            plan.thumbnailUrl = uploadBucket.url
          }
        }

        plan.useTransaction(trx)
        await plan.save()

        return plan
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      logger.error('Plans update error: %o', error)
      return response.badRequest(error)
    }
  }
}
