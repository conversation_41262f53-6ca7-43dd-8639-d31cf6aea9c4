import type { HttpContext } from '@adonisjs/core/http'
import { type JwtPayload, verify } from 'jsonwebtoken'
import emitter from '@adonisjs/core/services/emitter'
import stripe from 'stripe'

export default class WebhooksController {
  /**
   * Handle Wix webhook callbacks
   */
  async wixCallback({ request, response }: HttpContext) {
    console.log('wix callbacks', request.all())

    try {
      const jwtToken = request.raw()
      const jwtPayload = verify(jwtToken as string, process.env.WIX_PUBLIC_KEY as string)
      if (!jwtPayload) {
        return response.status(400)
      }

      const eventData = JSON.parse((jwtPayload as JwtPayload).data)
      const webhookData = JSON.parse(JSON.parse((jwtPayload as JwtPayload).data).data)
      console.log('webhooks event', eventData)
      console.log('webhooks data', webhookData)

      if (webhookData.actionEvent.body.newFulfillmentStatus === 'FULFILLED') {
        emitter.emit('webhook:wix', { email: webhookData.actionEvent.body.order.buyerInfo.email })
      }

      return response.status(200)
    } catch (error) {
      console.log('webhooks', error)
      return response.status(400).send({
        error,
        message: 'Failed to callback WIX',
      })
    }
  }

  /**
   * Handle Stripe webhook callbacks
   */
  async stripeCallback({ request, response }: HttpContext) {
    const stripeInstance = new stripe(process.env.STRIPE_SECRET_KEY!)
    const sig = request.headers()['stripe-signature']
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!

    let event

    try {
      event = stripeInstance.webhooks.constructEvent(request.raw() as string, sig!, endpointSecret)
    } catch (err) {
      console.log(err)
      return response.status(400).send(`Stripe webhook error: ${err.message}`)
    }

    console.log(event.type)

    try {
      // Handle different Stripe events
      switch (event.type) {
        case 'customer.created': {
          const customer = event.data.object
          emitter.emit('stripe:customer-created', customer)
          break
        }

        case 'customer.updated': {
          const updatedCustomer = event.data.object
          emitter.emit('stripe:customer-updated', updatedCustomer)
          break
        }

        case 'customer.subscription.created': {
          const subscription = event.data.object
          emitter.emit('stripe:subscription-created', subscription)
          break
        }

        case 'customer.subscription.updated': {
          const updatedSubscription = event.data.object
          emitter.emit('stripe:subscription-updated', updatedSubscription)
          break
        }

        case 'customer.subscription.deleted': {
          const deletedSubscription = event.data.object
          emitter.emit('stripe:subscription-deleted', deletedSubscription)
          break
        }

        case 'invoice.paid': {
          const paidInvoice = event.data.object
          emitter.emit('stripe:plan-purchased', paidInvoice)
          break
        }

        case 'invoice.payment_failed': {
          const paymentFailedInvoice = event.data.object
          console.log(`Invoice ${paymentFailedInvoice.id} payment failed!`)
          emitter.emit('stripe:plan-failed', paymentFailedInvoice)
          break
        }

        case 'customer.subscription.trial_will_end': {
          const trialWillEndSubscription = event.data.object
          console.log(`Subscription ${trialWillEndSubscription.id} trial will end!`)
          break
        }

        // Product webhook events
        case 'price.created': {
          const priceCreated = event.data.object
          emitter.emit('stripe:price-created', priceCreated)
          break
        }

        case 'price.updated': {
          const priceUpdated = event.data.object
          emitter.emit('stripe:price-updated', priceUpdated)
          break
        }

        case 'price.deleted': {
          const priceDeleted = event.data.object
          emitter.emit('stripe:price-deleted', priceDeleted)
          break
        }

        case 'product.created': {
          const productCreated = event.data.object
          emitter.emit('stripe:product-created', productCreated)
          break
        }

        case 'product.updated': {
          const productUpdated = event.data.object
          emitter.emit('stripe:product-updated', productUpdated)
          break
        }

        case 'product.deleted': {
          const productDeleted = event.data.object
          emitter.emit('stripe:product-deleted', productDeleted)
          break
        }

        default:
          console.log(`Unhandled event type ${event.type}`)
      }

      return response.status(200).send({ received: true })
    } catch (error) {
      console.log('Stripe webhook processing error:', error)
      return response.status(400).send({
        error: error.message || 'Webhook processing failed',
      })
    }
  }
}
