import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import vine from '@vinejs/vine'
import { omit } from 'radash'
import Randomstring from 'randomstring'

import PackCode from '#models/pack_code'

export default class PackCodesController {
  /**
   * Get active pack codes
   */
  public async findActivePackCodes({ response }: HttpContext) {
    try {
      const activeCodes = await db
        .from('pack_codes as a')
        .select(['b.title', 'b.language', 'b.region', 'a.pack_code'])
        .leftJoin('packs as b', 'a.pack_id', 'b.id')
        .whereNull('a.user_id')
        .orderBy('b.title')
        .orderBy('b.region')
        .orderBy('b.language')

      return response.send(activeCodes)
    } catch (error) {
      logger.error('PackCodes findActivePackCodes error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get all pack codes with filtering and pagination
   */
  public async find({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'sort', 'limit'])

      const packCodes = await PackCode.filter(filters)
        .preload('pack')
        .preload('user')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(packCodes)
    } catch (error) {
      logger.error('PackCodes find error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Create new pack codes
   */
  public async create({ request, response }: HttpContext) {
    try {
      const createPackCodeValidator = vine.compile(
        vine.object({
          pack_id: vine.number(),
          number_of_pack_codes: vine.number(),
        })
      )

      const validationData = await request.validateUsing(createPackCodeValidator)

      for (let i = 1; i <= validationData.number_of_pack_codes; i++) {
        let uid: string
        let isUnique = true
        do {
          uid = Randomstring.generate(6)
          const checkCode = await PackCode.findBy('pack_code', uid)
          if (checkCode) {
            isUnique = false
          } else {
            isUnique = true
          }
        } while (!isUnique)

        await db.transaction(async (trx) => {
          const newPackCode = new PackCode()
          newPackCode.packId = validationData.pack_id
          newPackCode.packCode = uid
          newPackCode.useTransaction(trx)
          await newPackCode.save()

          return newPackCode
        })
      }

      return response.status(200).send({ success: true, message: 'Successfully generated' })
    } catch (error) {
      logger.error('PackCodes create error: %o', error)
      return response.badRequest(error)
    }
  }
}
