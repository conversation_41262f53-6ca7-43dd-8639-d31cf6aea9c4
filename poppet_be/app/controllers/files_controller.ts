import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { v4 as uuid } from 'uuid'
import fs from 'node:fs'
import * as AWS from 'aws-sdk'

import User from '#models/user'
import { uploadFileValidator, uploadMyFileValidator } from '#validators/file_validator'

const { MediaConvert } = require('@aws-sdk/client-mediaconvert')

export const uploadToS3Bucket = async (
  file: any,
  bucket: string,
  userProfile?: string
): Promise<{ key: string; url: string }> => {
  const s3 = new AWS.S3({
    region: process.env.S3_REGION,
    secretAccessKey: process.env.S3_SECRET,
    accessKeyId: process.env.S3_KEY,
  })
  try {
    const { type, subtype, extname } = file
    const mimeType = `${type}/${subtype}`
    const name = userProfile ? `${userProfile}-${uuid()}.${extname}` : `${uuid()}.${extname}`

    const readStream = fs.createReadStream(file.tmpPath)
    await s3
      .upload({
        Key: name,
        Bucket: bucket,
        ContentType: mimeType,
        Body: readStream,
        ...(bucket === process.env.S3_BUCKET && { ACL: 'public-read' }),
      })
      .promise()
    const url = `https://${bucket}.s3.amazonaws.com/${name}`
    return {
      key: name,
      url,
    }
  } catch (err) {
    logger.error('S3 upload error: %o', err)
    throw err
  }
}

export const uploadToS3BucketWithFileName = async (
  file: any,
  bucket: string,
  userProfile?: string,
  filename?: string
): Promise<{ key: string; url: string }> => {
  const s3 = new AWS.S3({
    region: process.env.S3_REGION,
    secretAccessKey: process.env.S3_SECRET,
    accessKeyId: process.env.S3_KEY,
  })
  try {
    const { type, subtype, extname } = file
    const mimeType = `${type}/${subtype}`
    const name = userProfile
      ? `${userProfile}-${filename ?? uuid()}.${extname}`
      : `${filename ?? uuid()}.${extname}`

    const readStream = fs.createReadStream(file.tmpPath)
    await s3
      .upload({
        Key: name,
        Bucket: bucket,
        ContentType: mimeType,
        Body: readStream,
        ...(bucket === process.env.S3_BUCKET && { ACL: 'public-read' }),
      })
      .promise()
    const url = `https://${bucket}.s3.amazonaws.com/${name}`
    return {
      key: name,
      url,
    }
  } catch (err) {
    logger.error('S3 upload with filename error: %o', err)
    throw err
  }
}

const credentials: AWS.ConfigurationOptions = {
  region: process.env.S3_REGION,
  credentials: {
    accessKeyId: process.env.S3_KEY as string,
    secretAccessKey: process.env.S3_SECRET as string,
  },
}

// Configure AWS credentials and region
AWS.config.update({
  ...credentials,
  correctClockSkew: true,
})

const mediaConvert = new MediaConvert({
  ...credentials,
  endpoint: process.env.MEDIACONVERT_ENDPOINT,
})

const tenEighty = {
  NameModifier: '_1080p',
  VideoDescription: {
    Width: 1920,
    Height: 1080,
    CodecSettings: {
      Codec: 'H_264',
      H264Settings: {
        MaxBitrate: 5000000,
        RateControlMode: 'QVBR',
      },
    },
  },
  AudioDescriptions: [
    {
      CodecSettings: {
        Codec: 'AAC',
        AacSettings: {
          CodingMode: 'CODING_MODE_2_0',
          SampleRate: 48000,
          Bitrate: 96000,
        },
      },
    },
  ],
  ContainerSettings: {
    Container: 'M3U8',
    M3u8Settings: {},
  },
}

const sevenTwenty = {
  NameModifier: '_720p',
  VideoDescription: {
    Width: 1280,
    Height: 720,
    CodecSettings: {
      Codec: 'H_264',
      H264Settings: {
        MaxBitrate: 3000000,
        RateControlMode: 'QVBR',
      },
    },
  },
  AudioDescriptions: [
    {
      CodecSettings: {
        Codec: 'AAC',
        AacSettings: {
          CodingMode: 'CODING_MODE_2_0',
          SampleRate: 48000,
          Bitrate: 96000,
        },
      },
    },
  ],
  ContainerSettings: {
    Container: 'M3U8',
    M3u8Settings: {},
  },
}

const fourEighty = {
  NameModifier: '_480p',
  VideoDescription: {
    Width: 640,
    Height: 480,
    CodecSettings: {
      Codec: 'H_264',
      H264Settings: {
        MaxBitrate: 1080000,
        RateControlMode: 'QVBR',
      },
    },
  },
  AudioDescriptions: [
    {
      CodecSettings: {
        Codec: 'AAC',
        AacSettings: {
          CodingMode: 'CODING_MODE_2_0',
          SampleRate: 48000,
          Bitrate: 96000,
        },
      },
    },
  ],
  ContainerSettings: {
    Container: 'M3U8',
    M3u8Settings: {},
  },
}

export async function createMediaConvertJob(inputS3Url: string, outputS3Url: string) {
  const params = {
    Role: `arn:aws:iam::${process.env.AWS_ACCOUNT_ID}:role/service-role/MediaConvert_Default_Role`,
    Settings: {
      OutputGroups: [
        {
          Name: 'HLS Group',
          OutputGroupSettings: {
            Type: 'HLS_GROUP_SETTINGS',
            HlsGroupSettings: {
              SegmentLength: 10,
              MinSegmentLength: 10,
              Destination: outputS3Url,
            },
          },
          Outputs: [tenEighty, sevenTwenty, fourEighty],
        },
      ],
      Inputs: [
        {
          FileInput: inputS3Url,
          AudioSelectors: {
            'Audio Selector 1': {
              Offset: 0,
              DefaultSelection: 'DEFAULT',
              SelectorType: 'LANGUAGE_CODE',
              ProgramSelection: 1,
              LanguageCode: 'ENM',
            },
          },
        },
      ],
    },
  }

  const response = await mediaConvert.createJob(params)
  return response.Job
}

export default class FilesController {
  /**
   * Upload a file to S3 with type-specific bucket routing
   */
  public async uploadFile({ response, auth, request }: HttpContext) {
    try {
      const user = await User.findOrFail(auth?.user?.id)
      const validationData = await request.validateUsing(uploadFileValidator)

      if (validationData.file) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.file,
          validationData.type === 'video'
            ? (process.env.S3_BUCKET_VIDEO ?? '')
            : validationData.type === 'rive'
              ? (process.env.S3_BUCKET_RIVE ?? '')
              : 'hummusedu',
          user.email
        )

        if (uploadBucket.url) {
          return response.send({
            data: uploadBucket,
            success: true,
          })
        } else {
          return response.status(400).send({
            error: 'upload.image.failed',
            success: false,
          })
        }
      }
    } catch (error) {
      logger.error('File upload error: %o', error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }

  /**
   * Upload user's personal file to default S3 bucket
   */
  public async uploadMyFile({ response, request, auth }: HttpContext) {
    try {
      const validationData = await request.validateUsing(uploadMyFileValidator)
      const user = await User.findOrFail(auth?.user?.id)

      if (validationData.file) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.file,
          process.env.S3_BUCKET ?? 'hummusedu',
          user.email
        )

        if (uploadBucket?.url) {
          return response.send({
            data: uploadBucket.url,
            success: true,
          })
        } else {
          return response.status(400).send({
            success: false,
            data: null,
          })
        }
      }
    } catch (error) {
      logger.error('My file upload error: %o', error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }
}
