import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import vine from '@vinejs/vine'

import Bundle from '#models/bundle'
import User from '#models/user'
import Transaction from '#models/transaction'

export default class BundlesController {
  /**
   * Get all bundles with filtering and pagination
   */
  public async findAll({ request, response, auth }: HttpContext) {
    try {
      const user = await User.find(auth?.user?.id ?? 0)

      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')

      if (user === null) {
        const bundles = await Bundle.query()
          .where('blocked', false)
          .preload('stories', (query) => query.orderBy('id', 'desc'))
          .orderBy(sort[0], sort[1])
          .paginate(page, limit)
        return response.ok(bundles)
      }

      const bundles = await Bundle.query()
        .where('blocked', false)
        .preload('stories', (query) =>
          query
            .withCount('storyOrders', (nestedQuery) =>
              nestedQuery.where('user_id', user.id).where('blocked', false).as('redeemed')
            )
            .orderBy('id', 'desc')
        )
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(bundles)
    } catch (error) {
      logger.error('Bundles findAll error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get all bundles (admin endpoint)
   */
  public async adminFind({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')

      const bundles = await Bundle.query()
        .preload('stories')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(bundles)
    } catch (error) {
      logger.error('Bundles adminFind error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get a single bundle (admin endpoint)
   */
  public async adminFindOne({ response, params }: HttpContext) {
    try {
      const bundle = await Bundle.query().where('id', params.id).preload('stories')

      return response.ok(bundle)
    } catch (error) {
      logger.error('Bundles adminFindOne error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Create a new bundle (admin endpoint)
   */
  public async create({ request, response }: HttpContext) {
    try {
      const createBundleValidator = vine.compile(
        vine.object({
          title: vine.string().trim(),
          description: vine.string().trim().optional(),
          region: vine.string().trim(),
          language: vine.string().trim(),
          discount: vine.number(),
          blocked: vine.boolean(),
        })
      )

      const validationData = await request.validateUsing(createBundleValidator)

      const result = await db.transaction(async (trx) => {
        const newBundle = new Bundle()
        newBundle.title = validationData.title
        if (validationData.description) {
          newBundle.description = validationData.description
        }
        newBundle.region = validationData.region
        newBundle.language = validationData.language
        newBundle.discount = validationData.discount
        newBundle.blocked = validationData.blocked

        newBundle.useTransaction(trx)
        await newBundle.save()

        return newBundle
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      logger.error('Bundles create error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Update an existing bundle (admin endpoint)
   */
  public async update({ request, response, params }: HttpContext) {
    try {
      const bundle = await Bundle.findOrFail(params.id)

      const updateBundleValidator = vine.compile(
        vine.object({
          title: vine.string().trim(),
          description: vine.string().trim().optional(),
          region: vine.string().trim(),
          language: vine.string().trim(),
          discount: vine.number(),
          blocked: vine.boolean(),
        })
      )

      const validationData = await request.validateUsing(updateBundleValidator)

      const result = await db.transaction(async (trx) => {
        bundle.title = validationData.title
        if (validationData.description) {
          bundle.description = validationData.description
        }
        bundle.region = validationData.region
        bundle.language = validationData.language
        bundle.discount = validationData.discount
        bundle.blocked = validationData.blocked

        bundle.useTransaction(trx)
        await bundle.save()

        return bundle
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      logger.error('Bundles update error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Add stories to a bundle (admin endpoint)
   */
  public async addStoriesToBundle({ request, response, params }: HttpContext) {
    try {
      const bundle = await Bundle.findOrFail(params.id)

      const addStoriesValidator = vine.compile(
        vine.object({
          story_ids: vine.array(vine.number()),
        })
      )

      const validationData = await request.validateUsing(addStoriesValidator)

      await db.transaction(async (trx) => {
        // Only attach stories that are not already linked to this bundle
        const existingRows = await trx
          .from('bundle_stories')
          .where('bundle_id', bundle.id)
          .select('story_id')

        const existingSet = new Set<number>(
          existingRows.map((r: { story_id: number | string }) => Number(r.story_id))
        )

        const toAttach = validationData.story_ids.filter((id) => !existingSet.has(Number(id)))

        if (toAttach.length > 0) {
          await bundle.related('stories').attach(toAttach, trx)
        }
      })

      return response.ok({ success: true, message: 'Stories added to bundle successfully' })
    } catch (error) {
      logger.error('Bundles addStoriesToBundle error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Remove a story from a bundle (admin endpoint)
   */
  public async removeStoryFromBundle({ response, params }: HttpContext) {
    try {
      const bundle = await Bundle.findOrFail(params.id)
      const storyId = params.story_id

      await db.transaction(async (trx) => {
        // Remove story from bundle using the pivot table
        await bundle.related('stories').detach([storyId], trx)
      })

      return response.ok({ success: true, message: 'Story removed from bundle successfully' })
    } catch (error) {
      logger.error('Bundles removeStoryFromBundle error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Find bundle redemption data (admin endpoint)
   */
  public async findBundleRedemption({ response, request, params: { bundle_id } }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')

      const transactions = await Transaction.query()
        .where('title', `Redeemed Bundle (${bundle_id})`)
        .preload('user')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(transactions)
    } catch (error) {
      logger.error('Bundles findBundleRedemption error: %o', error)
      return response.badRequest(error)
    }
  }
}
