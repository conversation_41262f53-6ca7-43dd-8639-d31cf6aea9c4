import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import vine from '@vinejs/vine'
import File from '#models/file'
import User from '#models/user'
import { uploadToS3Bucket, uploadToS3BucketWithFileName } from './files_controller.js'
import PackLevel from '#models/pack_level'
import db from '@adonisjs/lucid/services/db'

export default class PackLevelsController {
  /**
   * Update pack level audio
   */
  public async updatePackLevelAudio({ request, response, auth, params }: HttpContext) {
    try {
      const user = await User.findOrFail(auth?.user?.id)

      const updatePackLevelAudioValidator = vine.compile(
        vine.object({
          audio: vine.file({
            extnames: ['mp3'],
            size: '10mb',
          }),
        })
      )

      const validationData = await request.validateUsing(updatePackLevelAudioValidator)

      let src: string
      if (validationData.audio) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.audio,
          process.env.S3_BUCKET ?? 'hummusedu',
          user.email
        )

        if (!uploadBucket?.url) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload',
          })
        }

        src = uploadBucket.url
      } else {
        return response.status(400).send({
          success: false,
          message: 'Audio file is required',
        })
      }

      const findPackLevel = await PackLevel.findOrFail(params.id)

      await db.transaction(async (trx) => {
        findPackLevel.audioUrl = src
        findPackLevel.useTransaction(trx)
        await findPackLevel.save()
      })

      return response.status(200).send({
        success: true,
      })
    } catch (error) {
      logger.error('PackLevels updatePackLevelAudio error: %o', error)
      return response.status(400).send({
        success: false,
        message: 'Failed to update pack level audio',
      })
    }
  }

  /**
   * Update pack level rive animation
   */
  public async updatePackLevelRive({ request, response, auth, params }: HttpContext) {
    try {
      await auth.authenticate()

      const updatePackLevelRiveValidator = vine.compile(
        vine.object({
          rive: vine.file({
            extnames: ['riv'],
            size: '3gb',
          }),
        })
      )

      const validationData = await request.validateUsing(updatePackLevelRiveValidator)

      let src: string
      if (validationData.rive) {
        const uploadBucket = await uploadToS3BucketWithFileName(
          validationData.rive,
          process.env.S3_BUCKET_RIVE ?? 'hummusedu',
          '',
          validationData.rive.clientName
        )

        if (!uploadBucket?.url || !uploadBucket?.key) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload',
          })
        }

        src = process.env.S3_CLOUDFRONT_RIVE + uploadBucket.key
      } else {
        return response.status(400).send({
          success: false,
          message: 'Rive file is required',
        })
      }

      const findPackLevel = await PackLevel.findOrFail(params.id)

      await db.transaction(async (trx) => {
        const newRive = new File()
        newRive.src = src
        newRive.type = 'rive'
        newRive.useTransaction(trx)
        await newRive.save()

        findPackLevel.fileId = newRive.id
        findPackLevel.useTransaction(trx)
        await findPackLevel.save()
      })

      return response.status(200).send({
        success: true,
      })
    } catch (error) {
      logger.error('PackLevels updatePackLevelRive error: %o', error)
      return response.status(400).send({
        success: false,
        message: 'Failed to update pack level rive',
      })
    }
  }
}
