import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import vine from '@vinejs/vine'

import User from '#models/user'
import { uploadToS3Bucket } from './files_controller.js'
import PackLevel from '#models/pack_level'
import db from '@adonisjs/lucid/services/db'

export default class PackLevelsController {
  /**
   * Update pack level audio
   */
  public async updatePackLevelAudio({ request, response, auth, params }: HttpContext) {
    try {
      const user = await User.findOrFail(auth?.user?.id)

      const updatePackLevelAudioValidator = vine.compile(
        vine.object({
          audio: vine.file({
            extnames: ['mp3'],
            size: '10mb',
          }),
        })
      )

      const validationData = await request.validateUsing(updatePackLevelAudioValidator)

      let src: string
      if (validationData.audio) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.audio,
          process.env.S3_BUCKET ?? 'hummusedu',
          user.email
        )

        if (!uploadBucket?.url) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload',
          })
        }

        src = uploadBucket.url
      } else {
        return response.status(400).send({
          success: false,
          message: 'Audio file is required',
        })
      }

      const findPackLevel = await PackLevel.findOrFail(params.id)

      await db.transaction(async (trx) => {
        findPackLevel.audioUrl = src
        findPackLevel.useTransaction(trx)
        await findPackLevel.save()
      })

      return response.status(200).send({
        success: true,
      })
    } catch (error) {
      logger.error('PackLevels updatePackLevelAudio error: %o', error)
      return response.status(400).send({
        success: false,
        message: 'Failed to update pack level audio',
      })
    }
  }
}
