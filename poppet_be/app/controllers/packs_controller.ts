import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { omit, keys } from 'radash'

import Pack from '#models/pack'
import User from '#models/user'
import UserPack from '#models/user_pack'

export default class PacksController {
  /**
   * Get all packs with filtering and pagination
   */
  public async findAll({ auth, request, response }: HttpContext) {
    try {
      const user = await User.find(auth?.user?.id ?? 0)
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      if (!keys(filters).includes('region')) {
        filters.region = 'sg'
      }

      // if logged in return user-preloaded packs
      // otherwise return all packs
      if (user === null) {
        const packs = await Pack.filter(filters)
          .preload('stories')
          .orderBy(sort[0], sort[1])
          .paginate(page, limit)

        return response.status(200).send(packs)
      }

      const packs = await Pack.filter(filters)
        .preload('stories')
        .withCount('userPacks', (query) => query.where('user_id', user.id).as('redeemed'))
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(packs)
    } catch (error) {
      logger.error('Packs findAll error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get all packs (admin endpoint)
   */
  public async adminFind({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      const packs = await Pack.filter(filters).orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.status(200).send(packs)
    } catch (error) {
      logger.error('Packs adminFind error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get a single pack by ID (admin endpoint)
   */
  public async adminFindOne({ params, response }: HttpContext) {
    try {
      const packs = await Pack.query()
        .where('id', params.id)
        .preload('packLevels', (query) => query.preload('file'))
        .first()

      return response.status(200).send(packs)
    } catch (error) {
      logger.error('Pack adminFindOne error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get user's packs
   */
  public async findMyPacks({ auth, request, response }: HttpContext) {
    try {
      const user = await auth.authenticate()
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      if (!keys(filters).includes('region')) {
        filters.region = user.region
      }

      const packs = await Pack.filter(filters)
        .select('packs.*')
        .leftJoin('user_packs', 'packs.id', 'user_packs.pack_id')
        .whereNotNull('user_packs.user_id')
        .whereNotNull('user_packs.pack_id')
        .where('user_packs.user_id', user.id)
        .withCount('userPacks', (query) => query.where('user_id', user.id).as('redeemed'))
        .preload('stories')
        .orderBy(`packs.${sort[0]}`, sort[1])
        .paginate(page, limit)

      return response.status(200).send(packs)
    } catch (error) {
      logger.error('Packs findMyPacks error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get a single pack by ID
   */
  public async findOne({ auth, response, params }: HttpContext) {
    try {
      const user = await User.find(auth?.user?.id ?? 0)

      // if logged in return user-preloaded pack
      // otherwise return normal pack
      if (user === null) {
        const pack = await Pack.query()
          .where('id', params.id)
          .preload('packLevels', (query) => query.preload('file'))
          .first()
        return response.status(200).send({ data: pack })
      }

      const pack = await Pack.query()
        .where('id', params.id)
        .withCount('userPacks', (query) => query.where('user_id', user.id).as('redeemed'))
        .preload('packLevels')
        .first()

      return response.send({
        data: pack,
      })
    } catch (error) {
      logger.error('Pack findOne error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get user packs
   */
  public async findUserPacks({ auth, request, response }: HttpContext) {
    try {
      const user = await auth.authenticate()
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      const userPack = await UserPack.filter(filters)
        .where('user_id', user.id)
        .preload('pack')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.send(userPack)
    } catch (error) {
      logger.error('UserPacks find error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get a specific user pack
   */
  public async findUserPack({ auth, response, params }: HttpContext) {
    try {
      const user = await auth.authenticate()
      const pack = await Pack.query().where('id', params.id).preload('stories').firstOrFail()

      const userPack = await UserPack.query()
        .where('user_id', user.id)
        .where('pack_id', pack.id)
        .preload('pack', (query) => {
          query.preload('stories').preload('packLevels', (query) => query.preload('file'))
        })
        .first()

      return response.send({
        data: userPack,
      })
    } catch (error) {
      logger.error('UserPack findOne error: %o', error)
      return response.status(400).send(error)
    }
  }
}
