import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import vine from '@vinejs/vine'

import Tag, { TagStatus } from '#models/tag'

export default class TagsController {
  /**
   * Get all active tags with pagination
   */
  public async index({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)

      const tags = await Tag.query()
        .where('status', 'active')
        .orderBy('name', 'asc')
        .paginate(page, limit)

      return response.ok(tags)
    } catch (error) {
      logger.error('Tags index error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get all tags with filtering (admin endpoint)
   */
  public async adminIndex({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const { status } = request.qs()

      const query = Tag.query()

      if (status) {
        query.where('status', status)
      }

      const tags = await query.orderBy('name', 'asc').paginate(page, limit)

      return response.ok(tags)
    } catch (error) {
      logger.error('Tags adminIndex error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Create a new tag
   */
  public async store({ request, response }: HttpContext) {
    try {
      const createTagValidator = vine.compile(
        vine.object({
          name: vine.string().trim(),
          status: vine.enum(Object.values(TagStatus)),
          imageUrl: vine.string().url().optional(),
        })
      )

      const validationData = await request.validateUsing(createTagValidator)
      const tag = await Tag.create(validationData)

      return response.created({ tag })
    } catch (error) {
      logger.error('Tags store error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Update an existing tag
   */
  public async update({ request, response, params }: HttpContext) {
    try {
      const tag = await Tag.findOrFail(params.id)

      const updateTagValidator = vine.compile(
        vine.object({
          name: vine.string().trim().optional(),
          status: vine.enum(Object.values(TagStatus)).optional(),
          imageUrl: vine.string().url().optional(),
        })
      )

      const validationData = await request.validateUsing(updateTagValidator)
      await tag.merge(validationData).save()

      return response.ok({ tag })
    } catch (error) {
      logger.error('Tags update error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Delete a tag
   */
  public async destroy({ response, params }: HttpContext) {
    try {
      const tag = await Tag.findOrFail(params.id)
      await tag.delete()

      return response.noContent()
    } catch (error) {
      logger.error('Tags destroy error: %o', error)
      return response.badRequest(error)
    }
  }
}
